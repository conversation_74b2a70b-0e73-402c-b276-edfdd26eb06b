#!/usr/bin/env node

/**
 * Debug Workflows - Verificar estado dos workflows criados
 */

require('dotenv').config();
const N8nApiClient = require('../maintenance/n8n_api_client.js');

// Configuração
const CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL || 'https://n8n-n8n.w9jo16.easypanel.host',
  n8nApiKey: process.env.N8N_API_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhcGkiLCJpYXQiOjE3NTYyMjEyMTEsImV4cCI6MTc1ODc3MjgwMH0.6BzCKbfcBOMtPukrVHFN1Mq2B4Fe8QezboF7CRp323M'
};

async function debugWorkflows() {
  try {
    const client = new N8nApiClient(CONFIG.n8nApiUrl, CONFIG.n8nApiKey);
    
    console.log('🔍 Verificando workflows criados...');
    
    // Listar todos os workflows
    const workflows = await client.getWorkflows();
    console.log(`📊 Total de workflows: ${workflows.data.length}`);
    
    // Procurar pelos workflows dos agentes
    const agentWorkflows = workflows.data.filter(wf => 
      wf.name.includes('Unified_User_Pipeline') || 
      wf.name.includes('Normalize_Extract_Subflow') ||
      wf.name.includes('User_Registry_Access_Subflow')
    );
    
    console.log('\n📋 Workflows dos Agentes:');
    for (const workflow of agentWorkflows) {
      console.log(`- ${workflow.name} (ID: ${workflow.id}) - Ativo: ${workflow.active}`);
      
      // Tentar obter detalhes do workflow
      try {
        const details = await client.getWorkflow(workflow.id);
        console.log(`  📝 Nós: ${details.nodes.length}`);
        console.log(`  🔗 Conexões: ${Object.keys(details.connections).length}`);
        
        // Verificar se há erros nos nós
        const nodeTypes = details.nodes.map(node => node.type);
        console.log(`  🧩 Tipos de nós: ${nodeTypes.join(', ')}`);
        
      } catch (error) {
        console.log(`  ❌ Erro ao obter detalhes: ${error.message}`);
      }
    }
    
    // Tentar ativar workflows inativos
    console.log('\n🔄 Tentando ativar workflows inativos...');
    for (const workflow of agentWorkflows) {
      if (!workflow.active) {
        try {
          console.log(`🔄 Ativando ${workflow.name}...`);
          await client.activateWorkflow(workflow.id);
          console.log(`✅ ${workflow.name} ativado com sucesso`);
        } catch (error) {
          console.log(`❌ Erro ao ativar ${workflow.name}: ${error.message}`);
          
          // Tentar obter mais detalhes do erro
          if (error.response && error.response.data) {
            console.log(`📋 Detalhes do erro:`, JSON.stringify(error.response.data, null, 2));
          }
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Erro no debug:', error);
  }
}

// Executar
debugWorkflows();

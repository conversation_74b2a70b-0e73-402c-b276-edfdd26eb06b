#!/usr/bin/env node

/**
 * Edit Agent 3 - User Registry Access Subflow
 * 
 * Edita especificamente o workflow existente #QmdVie0kPrUCx5VL
 * conforme as especificações do documento remote_agents_unificados.md
 * 
 * IMPORTANTE: NÃO criar novas credenciais - usar as existentes
 */

require('dotenv').config();
const N8nApiClient = require('../maintenance/n8n_api_client.js');

// Configuration
const CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL,
  n8nApiKey: process.env.N8N_API_KEY
};

// Workflow específico para editar
const WORKFLOW_ID = 'QmdVie0kPrUCx5VL';
const WORKFLOW_NAME = '03_User_Registry_Access_Subflow';

class Agent3Editor {
  constructor() {
    this.client = new N8nApiClient(CONFIG.n8nApiUrl, CONFIG.n8nApiKey);
  }

  // Criar estrutura do workflow conforme especificações
  createWorkflowStructure() {
    return {
      name: WORKFLOW_NAME,
      settings: {
        executionOrder: 'v1'
      },
      nodes: [
        // 1. Manual Trigger - Recebe dados do subfluxo anterior
        {
          id: 'manual-trigger',
          name: 'Manual Trigger',
          type: 'n8n-nodes-base.manualTrigger',
          position: [100, 200],
          parameters: {}
        },

        // 2. Postgres Node - Query complexa para buscar usuário por CPF
        {
          id: 'postgres-query',
          name: 'Buscar Usuário por CPF',
          type: 'n8n-nodes-base.postgres',
          position: [300, 200],
          parameters: {
            operation: 'executeQuery',
            query: `-- Buscar usuário completo por CPF
SELECT u.cpf, u.access_level, u.onboarding_progress, u.active,
       COUNT(cm.id) as message_count,
       MAX(cs.last_activity) as last_session
FROM users u
LEFT JOIN chat_sessions cs ON u.cpf = cs.user_cpf
LEFT JOIN chat_messages cm ON cs.id = cm.session_id
WHERE u.cpf = $1
GROUP BY u.cpf, u.access_level, u.onboarding_progress, u.active;`,
            additionalFields: {
              mode: 'list'
            }
          },
          credentials: {
            postgres: {
              id: 'assistente_v0.1',
              name: 'assistente_v0.1'
            }
          }
        },

        // 3. IF Node - Usuário existe?
        {
          id: 'if-user-exists',
          name: 'Usuário Existe?',
          type: 'n8n-nodes-base.if',
          position: [500, 200],
          parameters: {
            conditions: {
              options: {
                caseSensitive: true,
                leftValue: '',
                typeValidation: 'strict'
              },
              conditions: [
                {
                  leftValue: '={{ $json.cpf }}',
                  rightValue: '',
                  operator: {
                    operation: 'exists',
                    type: 'string'
                  }
                }
              ],
              combinator: 'and'
            }
          }
        },

        // 4. Code Node - Criar estrutura completa do novo usuário (Branch FALSE)
        {
          id: 'create-new-user',
          name: 'Criar Novo Usuário',
          type: 'n8n-nodes-base.code',
          position: [700, 300],
          parameters: {
            language: 'python',
            code: `# Estrutura de novo usuário - USAR PYTHON
from datetime import datetime
import json

def create_new_user(normalized_cpf, normalized_phone, push_name):
    new_user = {
        'cpf': normalized_cpf,
        'phone': normalized_phone,
        'name': push_name or 'Usuário',
        'access_level': 1,  # Iniciante
        'onboarding_progress': {
            'level': 1,
            'current_step': 'welcome',
            'completed_steps': [],
            'badges': [],
            'gamification_score': 0
        },
        'active': True,
        'metadata': {
            'first_contact': datetime.now().isoformat(),
            'source': 'whatsapp'
        }
    }
    return new_user

# Usar a função
input_data = _input.all()[0]['json']
new_user = create_new_user(
    input_data.get('cpf'),
    input_data.get('phone'),
    input_data.get('pushName')
)

return new_user`
          }
        },

        // 5. Supabase Node - Inserir novo usuário (Branch FALSE)
        {
          id: 'supabase-insert-user',
          name: 'Inserir Novo Usuário',
          type: 'n8n-nodes-base.supabase',
          position: [900, 300],
          parameters: {
            operation: 'insert',
            table: 'users',
            fieldsUi: {
              fieldValues: [
                {
                  fieldId: 'cpf',
                  fieldValue: '={{ $json.cpf }}'
                },
                {
                  fieldId: 'phone',
                  fieldValue: '={{ $json.phone }}'
                },
                {
                  fieldId: 'name',
                  fieldValue: '={{ $json.name }}'
                },
                {
                  fieldId: 'access_level',
                  fieldValue: '={{ $json.access_level }}'
                },
                {
                  fieldId: 'onboarding_progress',
                  fieldValue: '={{ JSON.stringify($json.onboarding_progress) }}'
                },
                {
                  fieldId: 'active',
                  fieldValue: '={{ $json.active }}'
                },
                {
                  fieldId: 'metadata',
                  fieldValue: '={{ JSON.stringify($json.metadata) }}'
                }
              ]
            }
          },
          credentials: {
            supabaseApi: {
              id: 'assistente_v0.1',
              name: 'Supabase assistente_v0.1'
            }
          }
        },

        // 6. Supabase Node - Atualizar last_access (Branch TRUE)
        {
          id: 'supabase-update-access',
          name: 'Atualizar Last Access',
          type: 'n8n-nodes-base.supabase',
          position: [700, 100],
          parameters: {
            operation: 'update',
            table: 'users',
            filterType: 'manual',
            matchingColumns: [
              {
                column: 'cpf',
                value: '={{ $json.cpf }}'
              }
            ],
            fieldsUi: {
              fieldValues: [
                {
                  fieldId: 'last_access',
                  fieldValue: '={{ new Date().toISOString() }}'
                }
              ]
            }
          },
          credentials: {
            supabaseApi: {
              id: 'assistente_v0.1',
              name: 'Supabase assistente_v0.1'
            }
          }
        },

        // 7. Code Node - Determinar access_level e permissions (Final)
        {
          id: 'determine-access-level',
          name: 'Determinar Access Level',
          type: 'n8n-nodes-base.code',
          position: [1100, 200],
          parameters: {
            language: 'python',
            code: `# Determinar access_level e permissions
def determine_permissions(access_level):
    permissions_map = {
        1: {'level': 'Iniciante', 'permissions': ['basic_chat', 'profile_view']},
        2: {'level': 'Explorador', 'permissions': ['basic_chat', 'profile_edit', 'survey_access']},
        3: {'level': 'Participante', 'permissions': ['basic_chat', 'profile_edit', 'community_access']},
        4: {'level': 'Colaborador', 'permissions': ['basic_chat', 'profile_edit', 'community_access', 'content_creation']},
        5: {'level': 'Especialista', 'permissions': ['basic_chat', 'profile_edit', 'community_access', 'content_creation', 'mentoring']},
        6: {'level': 'Líder', 'permissions': ['basic_chat', 'profile_edit', 'community_access', 'content_creation', 'mentoring', 'team_management']},
        7: {'level': 'Super Usuário', 'permissions': ['all_permissions', 'admin_access', 'system_management']}
    }
    return permissions_map.get(access_level, permissions_map[1])

# Processar dados do usuário
input_data = _input.all()[0]['json']
access_level = input_data.get('access_level', 1)
permissions = determine_permissions(access_level)

# Retornar dados completos do usuário
user_data = {
    'cpf': input_data.get('cpf'),
    'phone': input_data.get('phone'),
    'name': input_data.get('name'),
    'access_level': access_level,
    'permissions': permissions,
    'onboarding_progress': input_data.get('onboarding_progress', {}),
    'active': input_data.get('active', True),
    'last_access': input_data.get('last_access'),
    'message_count': input_data.get('message_count', 0),
    'last_session': input_data.get('last_session')
}

return user_data`
          }
        }
      ],
      connections: {
        'Manual Trigger': {
          main: [
            [
              {
                node: 'Buscar Usuário por CPF',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Buscar Usuário por CPF': {
          main: [
            [
              {
                node: 'Usuário Existe?',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Usuário Existe?': {
          main: [
            [
              {
                node: 'Atualizar Last Access',
                type: 'main',
                index: 0
              }
            ],
            [
              {
                node: 'Criar Novo Usuário',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Criar Novo Usuário': {
          main: [
            [
              {
                node: 'Inserir Novo Usuário',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Inserir Novo Usuário': {
          main: [
            [
              {
                node: 'Determinar Access Level',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Atualizar Last Access': {
          main: [
            [
              {
                node: 'Determinar Access Level',
                type: 'main',
                index: 0
              }
            ]
          ]
        }
      }
    };
  }

  // Editar o workflow existente
  async editWorkflow() {
    try {
      console.log(`🚀 Editando Agent 3 - User Registry Access Subflow...`);
      console.log(`📝 Workflow ID: ${WORKFLOW_ID}`);
      console.log(`📝 Workflow Name: ${WORKFLOW_NAME}`);

      // Verificar se o workflow existe
      const existingWorkflow = await this.client.getWorkflow(WORKFLOW_ID);
      if (!existingWorkflow) {
        throw new Error(`Workflow ${WORKFLOW_ID} não encontrado!`);
      }

      console.log(`✅ Workflow encontrado: ${existingWorkflow.name}`);

      // Criar nova estrutura
      const workflowData = this.createWorkflowStructure();
      
      console.log(`📤 Atualizando workflow "${WORKFLOW_NAME}" com nova estrutura...`);
      
      // Atualizar o workflow
      const result = await this.client.updateWorkflow(WORKFLOW_ID, workflowData);
      
      console.log(`✅ Agent 3 atualizado com sucesso!`);
      console.log(`📋 Workflow ID: ${WORKFLOW_ID}`);
      console.log(`📋 Workflow Name: ${WORKFLOW_NAME}`);
      
      return result;

    } catch (error) {
      console.error('❌ Erro ao editar Agent 3:', error.message);
      throw error;
    }
  }
}

// Executar se chamado diretamente
async function main() {
  if (!CONFIG.n8nApiKey) {
    console.error('❌ N8N_API_KEY environment variable is required');
    process.exit(1);
  }

  const editor = new Agent3Editor();
  
  try {
    await editor.editWorkflow();
    console.log('\n🎯 Agent 3 editado com sucesso conforme especificações!');
    process.exit(0);
  } catch (error) {
    console.error('\n❌ Falha ao editar Agent 3:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = Agent3Editor;

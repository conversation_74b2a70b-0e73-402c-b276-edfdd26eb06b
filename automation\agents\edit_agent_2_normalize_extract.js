#!/usr/bin/env node

/**
 * Edit Agent 2 - Normalize Extract Subflow
 * 
 * Edita especificamente o workflow existente #9bMvISktOxQkzwtj
 * conforme as especificações do documento remote_agents_unificados.md
 * 
 * IMPORTANTE: NÃO criar novas credenciais - usar as existentes
 */

require('dotenv').config();
const N8nApiClient = require('../maintenance/n8n_api_client.js');

// Configuration
const CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL,
  n8nApiKey: process.env.N8N_API_KEY
};

// Workflow específico para editar
const WORKFLOW_ID = '9bMvISktOxQkzwtj';
const WORKFLOW_NAME = '02_Normalize_Extract_Subflow';

class Agent2Editor {
  constructor() {
    this.client = new N8nApiClient(CONFIG.n8nApiUrl, CONFIG.n8nApiKey);
  }

  // Criar estrutura do workflow conforme especificações
  createWorkflowStructure() {
    return {
      name: WORKFLOW_NAME,
      settings: {
        executionOrder: 'v1'
      },
      nodes: [
        // 1. Manual Trigger - Subfluxo não tem webhook
        {
          id: 'manual-trigger',
          name: 'Manual Trigger',
          type: 'n8n-nodes-base.manualTrigger',
          position: [100, 200],
          parameters: {}
        },

        // 2. Code Node - Normalização de CPF, phone, mensagem
        {
          id: 'normalize-data',
          name: 'Normalizar Dados',
          type: 'n8n-nodes-base.code',
          position: [300, 200],
          parameters: {
            language: 'python',
            code: `# Normalização de dados de entrada do WhatsApp
import re
from datetime import datetime

def normalize_phone(phone):
    """Normaliza número de telefone brasileiro"""
    if not phone:
        return None
    
    # Remove todos os caracteres não numéricos
    clean_phone = re.sub(r'\\D', '', phone)
    
    # Adiciona código do país se não tiver
    if len(clean_phone) == 11 and clean_phone.startswith('0'):
        clean_phone = '55' + clean_phone[1:]
    elif len(clean_phone) == 10:
        clean_phone = '55' + clean_phone
    elif len(clean_phone) == 11 and not clean_phone.startswith('55'):
        clean_phone = '55' + clean_phone
    
    return clean_phone

def extract_cpf(text):
    """Extrai CPF de texto (se presente)"""
    if not text:
        return None
    
    # Padrão para CPF: 000.000.000-00 ou 00000000000
    cpf_pattern = r'\\b\\d{3}\\.?\\d{3}\\.?\\d{3}-?\\d{2}\\b'
    match = re.search(cpf_pattern, text)
    
    if match:
        cpf = re.sub(r'\\D', '', match.group())
        if len(cpf) == 11:
            return cpf
    
    return None

def normalize_message(message):
    """Normaliza mensagem de texto"""
    if not message:
        return ""
    
    # Remove espaços extras e quebras de linha desnecessárias
    normalized = re.sub(r'\\s+', ' ', message.strip())
    
    return normalized

# Processar dados de entrada
input_data = _input.all()[0]['json']

# Extrair dados do webhook
phone = input_data.get('data', {}).get('key', {}).get('remoteJid', '')
message_text = input_data.get('data', {}).get('message', {}).get('conversation', '')
push_name = input_data.get('data', {}).get('pushName', '')
message_type = input_data.get('data', {}).get('messageType', 'text')

# Normalizar dados
normalized_phone = normalize_phone(phone.replace('@s.whatsapp.net', ''))
normalized_message = normalize_message(message_text)
extracted_cpf = extract_cpf(message_text)

# Estrutura de dados normalizada
normalized_data = {
    'phone': normalized_phone,
    'message': normalized_message,
    'pushName': push_name,
    'messageType': message_type,
    'cpf': extracted_cpf,
    'timestamp': datetime.now().isoformat(),
    'source': 'whatsapp',
    'raw_data': input_data,
    'metadata': {
        'normalized_at': datetime.now().isoformat(),
        'phone_original': phone,
        'message_length': len(normalized_message) if normalized_message else 0,
        'has_cpf': bool(extracted_cpf)
    }
}

return normalized_data`
          }
        },

        // 3. Set Node - Estruturação de dados padronizada
        {
          id: 'set-structured-data',
          name: 'Estruturar Dados',
          type: 'n8n-nodes-base.set',
          position: [500, 200],
          parameters: {
            values: {
              values: [
                {
                  name: 'phone',
                  value: '={{ $json.phone }}'
                },
                {
                  name: 'message',
                  value: '={{ $json.message }}'
                },
                {
                  name: 'pushName',
                  value: '={{ $json.pushName }}'
                },
                {
                  name: 'cpf',
                  value: '={{ $json.cpf }}'
                },
                {
                  name: 'messageType',
                  value: '={{ $json.messageType }}'
                },
                {
                  name: 'timestamp',
                  value: '={{ $json.timestamp }}'
                },
                {
                  name: 'source',
                  value: '={{ $json.source }}'
                },
                {
                  name: 'metadata',
                  value: '={{ $json.metadata }}'
                }
              ]
            },
            options: {
              dotNotation: false
            }
          }
        },

        // 4. Code Node - Validação final e limpeza
        {
          id: 'validate-clean-data',
          name: 'Validar e Limpar',
          type: 'n8n-nodes-base.code',
          position: [700, 200],
          parameters: {
            language: 'python',
            code: `# Validação final e limpeza de dados
def validate_phone(phone):
    """Valida se o telefone está no formato correto"""
    if not phone:
        return False
    
    # Deve ter 13 dígitos (55 + 11 dígitos do número)
    if len(phone) == 13 and phone.startswith('55'):
        return True
    
    return False

def validate_cpf(cpf):
    """Valida CPF usando algoritmo oficial"""
    if not cpf or len(cpf) != 11:
        return False
    
    # Verifica se todos os dígitos são iguais
    if cpf == cpf[0] * 11:
        return False
    
    # Calcula primeiro dígito verificador
    sum1 = sum(int(cpf[i]) * (10 - i) for i in range(9))
    digit1 = 11 - (sum1 % 11)
    if digit1 >= 10:
        digit1 = 0
    
    # Calcula segundo dígito verificador
    sum2 = sum(int(cpf[i]) * (11 - i) for i in range(10))
    digit2 = 11 - (sum2 % 11)
    if digit2 >= 10:
        digit2 = 0
    
    # Verifica se os dígitos calculados conferem
    return cpf[-2:] == f"{digit1}{digit2}"

# Processar dados de entrada
input_data = _input.all()[0]['json']

# Validar dados
phone_valid = validate_phone(input_data.get('phone'))
cpf_valid = validate_cpf(input_data.get('cpf')) if input_data.get('cpf') else None

# Dados finais validados e limpos
validated_data = {
    'phone': input_data.get('phone') if phone_valid else None,
    'message': input_data.get('message', '').strip(),
    'pushName': input_data.get('pushName', '').strip(),
    'cpf': input_data.get('cpf') if cpf_valid else None,
    'messageType': input_data.get('messageType', 'text'),
    'timestamp': input_data.get('timestamp'),
    'source': input_data.get('source', 'whatsapp'),
    'validation': {
        'phone_valid': phone_valid,
        'cpf_valid': cpf_valid,
        'has_message': bool(input_data.get('message', '').strip()),
        'has_push_name': bool(input_data.get('pushName', '').strip())
    },
    'metadata': input_data.get('metadata', {})
}

return validated_data`
          }
        }
      ],
      connections: {
        'Manual Trigger': {
          main: [
            [
              {
                node: 'Normalizar Dados',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Normalizar Dados': {
          main: [
            [
              {
                node: 'Estruturar Dados',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Estruturar Dados': {
          main: [
            [
              {
                node: 'Validar e Limpar',
                type: 'main',
                index: 0
              }
            ]
          ]
        }
      }
    };
  }

  // Editar o workflow existente
  async editWorkflow() {
    try {
      console.log(`🚀 Editando Agent 2 - Normalize Extract Subflow...`);
      console.log(`📝 Workflow ID: ${WORKFLOW_ID}`);
      console.log(`📝 Workflow Name: ${WORKFLOW_NAME}`);

      // Verificar se o workflow existe
      const existingWorkflow = await this.client.getWorkflow(WORKFLOW_ID);
      if (!existingWorkflow) {
        throw new Error(`Workflow ${WORKFLOW_ID} não encontrado!`);
      }

      console.log(`✅ Workflow encontrado: ${existingWorkflow.name}`);

      // Criar nova estrutura
      const workflowData = this.createWorkflowStructure();
      
      console.log(`📤 Atualizando workflow "${WORKFLOW_NAME}" com nova estrutura...`);
      
      // Atualizar o workflow
      const result = await this.client.updateWorkflow(WORKFLOW_ID, workflowData);
      
      console.log(`✅ Agent 2 atualizado com sucesso!`);
      console.log(`📋 Workflow ID: ${WORKFLOW_ID}`);
      console.log(`📋 Workflow Name: ${WORKFLOW_NAME}`);
      
      return result;

    } catch (error) {
      console.error('❌ Erro ao editar Agent 2:', error.message);
      throw error;
    }
  }
}

// Executar se chamado diretamente
async function main() {
  if (!CONFIG.n8nApiKey) {
    console.error('❌ N8N_API_KEY environment variable is required');
    process.exit(1);
  }

  const editor = new Agent2Editor();
  
  try {
    await editor.editWorkflow();
    console.log('\n🎯 Agent 2 editado com sucesso conforme especificações!');
    process.exit(0);
  } catch (error) {
    console.error('\n❌ Falha ao editar Agent 2:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = Agent2Editor;

#!/usr/bin/env node

/**
 * Agent 2 - Normalize Extract Subflow
 * 
 * Implementa o subfluxo de normalização e extração de dados
 * - Normalização de phone (+55 formato)
 * - Limpeza de mensagem (text/mídia)
 * - Estruturação de dados (Python)
 * - Preservação dados originais
 * 
 * Baseado em: REMOTE_AGENTS_FINAL_CONSOLIDADO.md
 */

require('dotenv').config();
const N8nApiClient = require('../maintenance/n8n_api_client.js');

// Configuração
const CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL || 'https://n8n-n8n.w9jo16.easypanel.host',
  n8nApiKey: process.env.N8N_API_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhcGkiLCJpYXQiOjE3NTYyMjEyMTEsImV4cCI6MTc1ODc3MjgwMH0.6BzCKbfcBOMtPukrVHFN1Mq2B4Fe8QezboF7CRp323M'
};

class Agent2NormalizeExtractSubflow {
  constructor() {
    this.client = new N8nApiClient(CONFIG.n8nApiUrl, CONFIG.n8nApiKey);
    this.workflowName = 'Normalize_Extract_Subflow';
  }

  // Criar estrutura do workflow
  createWorkflowStructure() {
    return {
      name: this.workflowName,
      nodes: [
        // 1. Execute Workflow Trigger - Subfluxo chamado pelo workflow principal
        {
          id: 'execute-workflow-trigger',
          name: 'Execute Workflow Trigger',
          type: 'n8n-nodes-base.executeWorkflowTrigger',
          typeVersion: 1,
          position: [240, 300]
        },

        // 2. Code Node - Normalização de phone
        {
          id: 'normalize-phone',
          name: 'Normalize Phone',
          type: 'n8n-nodes-base.code',
          typeVersion: 2,
          position: [460, 300],
          parameters: {
            language: 'python',
            code: `# Normalização de telefone para formato +55
import re

# Dados de entrada
input_data = items[0]['json']
phone = input_data.get('phone', '')

# Função para normalizar telefone brasileiro
def normalize_brazilian_phone(phone_number):
    if not phone_number:
        return ''
    
    # Remover todos os caracteres não numéricos
    clean_phone = re.sub(r'\\D', '', str(phone_number))
    
    # Se começar com 55, assumir que já tem código do país
    if clean_phone.startswith('55'):
        # Verificar se tem 13 dígitos (55 + 11 dígitos)
        if len(clean_phone) == 13:
            return '+' + clean_phone
        # Se tem 12 dígitos, adicionar 9 no celular
        elif len(clean_phone) == 12:
            # Adicionar 9 após o DDD se for celular
            ddd = clean_phone[2:4]
            numero = clean_phone[4:]
            if len(numero) == 8:  # Celular sem 9
                return '+55' + ddd + '9' + numero
            else:
                return '+' + clean_phone
    
    # Se não tem código do país, assumir Brasil (+55)
    if len(clean_phone) == 11:  # DDD + 9 dígitos
        return '+55' + clean_phone
    elif len(clean_phone) == 10:  # DDD + 8 dígitos (adicionar 9)
        ddd = clean_phone[:2]
        numero = clean_phone[2:]
        return '+55' + ddd + '9' + numero
    
    # Se não conseguir normalizar, retornar original com +55
    return '+55' + clean_phone

# Normalizar telefone
normalized_phone = normalize_brazilian_phone(phone)

# Preservar dados originais e adicionar normalizado
result = input_data.copy()
result['normalized_phone'] = normalized_phone
result['original_phone'] = phone

return [{'json': result}]`
          }
        },

        // 3. Code Node - Limpeza de mensagem
        {
          id: 'clean-message',
          name: 'Clean Message',
          type: 'n8n-nodes-base.code',
          typeVersion: 2,
          position: [680, 300],
          parameters: {
            language: 'python',
            code: `# Limpeza e estruturação de mensagem
import re
import json

# Dados de entrada
input_data = items[0]['json']
message_body = input_data.get('message_body', '')
message_type = input_data.get('message_type', 'text')

# Função para limpar mensagem de texto
def clean_text_message(text):
    if not text:
        return ''
    
    # Remover caracteres especiais problemáticos
    # Manter acentos e pontuação básica
    cleaned = re.sub(r'[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]', '', str(text))
    
    # Normalizar espaços
    cleaned = re.sub(r'\\s+', ' ', cleaned).strip()
    
    return cleaned

# Função para extrair informações da mensagem
def extract_message_info(text, msg_type):
    info = {
        'type': msg_type,
        'content': text,
        'length': len(text) if text else 0,
        'has_emoji': bool(re.search(r'[\\U0001F600-\\U0001F64F\\U0001F300-\\U0001F5FF\\U0001F680-\\U0001F6FF\\U0001F1E0-\\U0001F1FF]', text)) if text else False,
        'has_url': bool(re.search(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', text)) if text else False,
        'word_count': len(text.split()) if text else 0
    }
    
    return info

# Processar mensagem baseado no tipo
if message_type == 'text':
    cleaned_message = clean_text_message(message_body)
    message_info = extract_message_info(cleaned_message, message_type)
else:
    # Para mídia, manter informação básica
    cleaned_message = f"[{message_type.upper()}]"
    message_info = {
        'type': message_type,
        'content': cleaned_message,
        'length': 0,
        'has_emoji': False,
        'has_url': False,
        'word_count': 0,
        'is_media': True
    }

# Preservar dados originais e adicionar processados
result = input_data.copy()
result['cleaned_message'] = cleaned_message
result['original_message'] = message_body
result['message_info'] = message_info

return [{'json': result}]`
          }
        },

        // 4. Set Node - Estruturação de dados padronizada
        {
          id: 'structure-data',
          name: 'Structure Data',
          type: 'n8n-nodes-base.set',
          typeVersion: 3,
          position: [900, 300],
          parameters: {
            assignments: {
              assignments: [
                {
                  id: 'phone',
                  name: 'phone',
                  value: '={{ $json.normalized_phone }}',
                  type: 'string'
                },
                {
                  id: 'message',
                  name: 'message',
                  value: '={{ $json.cleaned_message }}',
                  type: 'string'
                },
                {
                  id: 'push_name',
                  name: 'push_name',
                  value: '={{ $json.push_name }}',
                  type: 'string'
                },
                {
                  id: 'timestamp',
                  name: 'timestamp',
                  value: '={{ $json.timestamp }}',
                  type: 'string'
                },
                {
                  id: 'message_type',
                  name: 'message_type',
                  value: '={{ $json.message_type }}',
                  type: 'string'
                },
                {
                  id: 'message_info',
                  name: 'message_info',
                  value: '={{ $json.message_info }}',
                  type: 'object'
                },
                {
                  id: 'original_data',
                  name: 'original_data',
                  value: '={{ $json.original_data }}',
                  type: 'object'
                }
              ]
            },
            options: {}
          }
        },

        // 5. Code Node - Validação final e limpeza
        {
          id: 'final-validation',
          name: 'Final Validation',
          type: 'n8n-nodes-base.code',
          typeVersion: 2,
          position: [1120, 300],
          parameters: {
            language: 'python',
            code: `# Validação final e preparação para próximo subfluxo
import re

# Dados de entrada
input_data = items[0]['json']

# Validações
def validate_phone(phone):
    if not phone:
        return False, "Phone is empty"
    
    # Verificar formato +55XXXXXXXXXXX
    if not re.match(r'^\\+55\\d{10,11}$', phone):
        return False, f"Invalid phone format: {phone}"
    
    return True, "Valid"

def validate_message(message, message_type):
    if message_type == 'text' and not message:
        return False, "Text message is empty"
    
    if message_type != 'text' and not message.startswith('['):
        return False, "Media message not properly formatted"
    
    return True, "Valid"

# Executar validações
phone_valid, phone_msg = validate_phone(input_data.get('phone'))
message_valid, message_msg = validate_message(
    input_data.get('message'), 
    input_data.get('message_type')
)

# Preparar resultado final
result = {
    'phone': input_data.get('phone'),
    'message': input_data.get('message'),
    'push_name': input_data.get('push_name'),
    'timestamp': input_data.get('timestamp'),
    'message_type': input_data.get('message_type'),
    'message_info': input_data.get('message_info'),
    'original_data': input_data.get('original_data'),
    'validation': {
        'phone_valid': phone_valid,
        'phone_message': phone_msg,
        'message_valid': message_valid,
        'message_message': message_msg,
        'overall_valid': phone_valid and message_valid
    },
    'processed_at': input_data.get('timestamp'),
    'subflow': 'normalize_extract'
}

return [{'json': result}]`
          }
        }
      ],

      // Conexões entre os nós
      connections: {
        'Execute Workflow Trigger': {
          main: [
            [
              {
                node: 'Normalize Phone',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Normalize Phone': {
          main: [
            [
              {
                node: 'Clean Message',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Clean Message': {
          main: [
            [
              {
                node: 'Structure Data',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Structure Data': {
          main: [
            [
              {
                node: 'Final Validation',
                type: 'main',
                index: 0
              }
            ]
          ]
        }
      },

      settings: {
        executionOrder: 'v1'
      }
    };
  }

  // Implementar o workflow
  async implement() {
    try {
      console.log('🚀 Iniciando implementação do Agent 2 - Normalize Extract Subflow...');
      
      // Verificar se workflow já existe
      const existingWorkflow = await this.client.findWorkflowByName(this.workflowName);
      
      // Criar estrutura do workflow
      const workflowData = this.createWorkflowStructure();
      
      let result;
      if (existingWorkflow) {
        console.log(`📝 Atualizando workflow existente: ${this.workflowName}`);
        result = await this.client.updateWorkflow(existingWorkflow.id, workflowData);
      } else {
        console.log(`🆕 Criando novo workflow: ${this.workflowName}`);
        result = await this.client.createWorkflow(workflowData);
      }
      
      // Ativar o workflow
      if (result.id) {
        console.log(`🔄 Ativando workflow: ${this.workflowName}`);
        await this.client.activateWorkflow(result.id);
      }
      
      console.log(`✅ Agent 2 implementado com sucesso!`);
      console.log(`📋 Workflow ID: ${result.id}`);
      
      return result;
      
    } catch (error) {
      console.error('❌ Erro na implementação do Agent 2:', error);
      throw error;
    }
  }

  // Testar o workflow
  async test() {
    try {
      console.log('🧪 Testando Agent 2...');
      
      // Dados de teste
      const testData = {
        phone: '11999999999',
        message_body: 'Olá! Como está? 😊 Visite https://example.com',
        push_name: 'Usuário Teste',
        timestamp: Date.now(),
        message_type: 'text'
      };
      
      console.log('📤 Dados de teste:', testData);
      console.log('✅ Teste preparado - execute manualmente no n8n');
      
    } catch (error) {
      console.error('❌ Erro no teste do Agent 2:', error);
      throw error;
    }
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  const agent = new Agent2NormalizeExtractSubflow();
  
  agent.implement()
    .then(() => agent.test())
    .then(() => {
      console.log('🎉 Agent 2 - Normalize Extract Subflow implementado e testado com sucesso!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Falha na implementação:', error);
      process.exit(1);
    });
}

module.exports = Agent2NormalizeExtractSubflow;

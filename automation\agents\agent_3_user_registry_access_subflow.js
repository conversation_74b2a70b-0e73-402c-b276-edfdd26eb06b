#!/usr/bin/env node

/**
 * Agent 3 - User Registry Access Subflow
 * 
 * Implementa o subfluxo de acesso ao registro de usuários
 * - Query Supabase buscar usuário por phone
 * - Criação novo usuário (se não existir)
 * - Busca phone → CPF
 * - Determinação access_level
 * - Retorno dados completos usuário
 * 
 * Baseado em: REMOTE_AGENTS_FINAL_CONSOLIDADO.md
 */

require('dotenv').config();
const N8nApiClient = require('../maintenance/n8n_api_client.js');

// Configuração
const CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL || 'https://n8n-n8n.w9jo16.easypanel.host',
  n8nApiKey: process.env.N8N_API_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhcGkiLCJpYXQiOjE3NTYyMjEyMTEsImV4cCI6MTc1ODc3MjgwMH0.6BzCKbfcBOMtPukrVHFN1Mq2B4Fe8QezboF7CRp323M'
};

class Agent3UserRegistryAccessSubflow {
  constructor() {
    this.client = new N8nApiClient(CONFIG.n8nApiUrl, CONFIG.n8nApiKey);
    this.workflowName = 'User_Registry_Access_Subflow';
  }

  // Criar estrutura do workflow
  createWorkflowStructure() {
    return {
      name: this.workflowName,
      nodes: [
        // 1. Manual Trigger - Recebe dados do subfluxo anterior
        {
          id: 'manual-trigger',
          name: 'Manual Trigger',
          type: 'n8n-nodes-base.manualTrigger',
          typeVersion: 1,
          position: [240, 300]
        },

        // 2. Postgres Node - Query complexa para buscar usuário por CPF
        {
          id: 'search-user-by-cpf',
          name: 'Search User by CPF',
          type: 'n8n-nodes-base.postgres',
          typeVersion: 2,
          position: [460, 300],
          parameters: {
            operation: 'executeQuery',
            query: `-- Buscar usuário completo por CPF
SELECT u.cpf, u.access_level, u.onboarding_progress, u.active,
       COUNT(cm.id) as message_count,
       MAX(cs.last_activity) as last_session
FROM users u
LEFT JOIN chat_sessions cs ON u.cpf = cs.user_cpf
LEFT JOIN chat_messages cm ON cs.id = cm.session_id
WHERE u.cpf = $1
GROUP BY u.cpf, u.access_level, u.onboarding_progress, u.active;`,
            additionalFields: {
              mode: 'list',
              queryParams: '={{ [$json.cpf] }}'
            }
          },
          credentials: {
            postgres: {
              id: 'postgres-credentials',
              name: 'PostgreSQL'
            }
          }
        },

        // 3. IF Node - Usuário existe?
        {
          id: 'user-exists-check',
          name: 'User Exists?',
          type: 'n8n-nodes-base.if',
          typeVersion: 2,
          position: [680, 300],
          parameters: {
            conditions: {
              options: {
                caseSensitive: true,
                leftValue: '',
                typeValidation: 'strict'
              },
              conditions: [
                {
                  id: 'user-found',
                  leftValue: '={{ $json.length }}',
                  rightValue: 0,
                  operator: {
                    type: 'number',
                    operation: 'gt'
                  }
                }
              ],
              combinator: 'and'
            }
          }
        },

        // 4. Code Node - Criar novo usuário (branch FALSE do IF)
        {
          id: 'create-new-user',
          name: 'Create New User',
          type: 'n8n-nodes-base.code',
          typeVersion: 2,
          position: [900, 400],
          parameters: {
            language: 'python',
            code: `# Estrutura de novo usuário - USAR PYTHON
from datetime import datetime
import json

def create_new_user(normalized_cpf, normalized_phone, push_name):
    new_user = {
        'cpf': normalized_cpf,
        'phone': normalized_phone,
        'name': push_name or 'Usuário',
        'access_level': 1,  # Iniciante
        'onboarding_progress': {
            'level': 1,
            'current_step': 'welcome',
            'completed_steps': [],
            'badges': [],
            'gamification_score': 0
        },
        'active': True,
        'metadata': {
            'first_contact': datetime.now().isoformat(),
            'source': 'whatsapp'
        }
    }
    return new_user

# Usar a função
input_data = items[0]['json']
new_user = create_new_user(
    input_data.get('cpf'),
    input_data.get('phone'),
    input_data.get('push_name')
)

return [{'json': new_user}]`
          }
        },

        // 5. Supabase Node - Inserir novo usuário no banco
        {
          id: 'insert-new-user',
          name: 'Insert New User',
          type: 'n8n-nodes-base.supabase',
          typeVersion: 1,
          position: [1120, 400],
          parameters: {
            operation: 'insert',
            table: 'users',
            fieldsUi: {
              fieldValues: [
                {
                  fieldId: 'cpf',
                  fieldValue: '={{ $json.cpf }}'
                },
                {
                  fieldId: 'phone',
                  fieldValue: '={{ $json.phone }}'
                },
                {
                  fieldId: 'name',
                  fieldValue: '={{ $json.name }}'
                },
                {
                  fieldId: 'access_level',
                  fieldValue: '={{ $json.access_level }}'
                },
                {
                  fieldId: 'onboarding_progress',
                  fieldValue: '={{ JSON.stringify($json.onboarding_progress) }}'
                },
                {
                  fieldId: 'active',
                  fieldValue: '={{ $json.active }}'
                },
                {
                  fieldId: 'metadata',
                  fieldValue: '={{ JSON.stringify($json.metadata) }}'
                }
              ]
            }
          },
          credentials: {
            supabaseApi: {
              id: 'supabase-credentials',
              name: 'Supabase API'
            }
          }
        },

        // 6. Supabase Node - Atualizar last_access (branch TRUE do IF)
        {
          id: 'update-last-access',
          name: 'Update Last Access',
          type: 'n8n-nodes-base.supabase',
          typeVersion: 1,
          position: [900, 200],
          parameters: {
            operation: 'update',
            table: 'users',
            filterType: 'manual',
            matchType: 'allFilters',
            filters: {
              conditions: [
                {
                  keyName: 'cpf',
                  condition: 'equals',
                  keyValue: '={{ $("Manual Trigger").item.json.cpf }}'
                }
              ]
            },
            fieldsUi: {
              fieldValues: [
                {
                  fieldId: 'last_access',
                  fieldValue: '={{ new Date().toISOString() }}'
                }
              ]
            }
          },
          credentials: {
            supabaseApi: {
              id: 'supabase-credentials',
              name: 'Supabase API'
            }
          }
        },

        // 7. Code Node - Determinar access_level e preparar resposta
        {
          id: 'determine-access-level',
          name: 'Determine Access Level',
          type: 'n8n-nodes-base.code',
          typeVersion: 2,
          position: [1340, 300],
          parameters: {
            language: 'python',
            code: `# Determinar access_level e preparar dados do usuário
import json
from datetime import datetime

# Dados de entrada
input_data = items[0]['json']

# Verificar se é usuário existente (vem do Postgres) ou novo (vem do Code)
if 'message_count' in input_data:
    # Usuário existente - dados do Postgres
    user_data = {
        'cpf': input_data.get('cpf'),
        'access_level': input_data.get('access_level', 1),
        'onboarding_progress': json.loads(input_data.get('onboarding_progress', '{}')),
        'active': input_data.get('active', True),
        'message_count': input_data.get('message_count', 0),
        'last_session': input_data.get('last_session'),
        'user_type': 'existing'
    }
else:
    # Usuário novo - dados do Code Node
    user_data = {
        'cpf': input_data.get('cpf'),
        'phone': input_data.get('phone'),
        'name': input_data.get('name'),
        'access_level': input_data.get('access_level', 1),
        'onboarding_progress': input_data.get('onboarding_progress', {}),
        'active': input_data.get('active', True),
        'metadata': input_data.get('metadata', {}),
        'message_count': 0,
        'user_type': 'new'
    }

# Determinar permissões baseado no access_level
access_level = user_data['access_level']
permissions = {
    'can_send_messages': True,
    'can_receive_responses': True,
    'can_access_ai': access_level >= 2,
    'can_access_advanced_features': access_level >= 3,
    'can_manage_data': access_level >= 5,
    'is_admin': access_level >= 7
}

# Preparar resultado final
result = {
    'user': user_data,
    'permissions': permissions,
    'access_level': access_level,
    'cpf': user_data['cpf'],
    'user_type': user_data['user_type'],
    'message_count': user_data.get('message_count', 0),
    'onboarding_progress': user_data.get('onboarding_progress', {}),
    'processed_at': datetime.now().isoformat(),
    'subflow': 'user_registry_access'
}

return [{'json': result}]`
          }
        }
      ],

      // Conexões entre os nós
      connections: {
        'Manual Trigger': {
          main: [
            [
              {
                node: 'Search User by CPF',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Search User by CPF': {
          main: [
            [
              {
                node: 'User Exists?',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'User Exists?': {
          main: [
            [
              {
                node: 'Update Last Access',
                type: 'main',
                index: 0
              }
            ],
            [
              {
                node: 'Create New User',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Update Last Access': {
          main: [
            [
              {
                node: 'Determine Access Level',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Create New User': {
          main: [
            [
              {
                node: 'Insert New User',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Insert New User': {
          main: [
            [
              {
                node: 'Determine Access Level',
                type: 'main',
                index: 0
              }
            ]
          ]
        }
      },

      settings: {
        executionOrder: 'v1'
      }
    };
  }

  // Implementar o workflow
  async implement() {
    try {
      console.log('🚀 Iniciando implementação do Agent 3 - User Registry Access Subflow...');
      
      // Verificar se workflow já existe
      const existingWorkflow = await this.client.findWorkflowByName(this.workflowName);
      
      // Criar estrutura do workflow
      const workflowData = this.createWorkflowStructure();
      
      let result;
      if (existingWorkflow) {
        console.log(`📝 Atualizando workflow existente: ${this.workflowName}`);
        result = await this.client.updateWorkflow(existingWorkflow.id, workflowData);
      } else {
        console.log(`🆕 Criando novo workflow: ${this.workflowName}`);
        result = await this.client.createWorkflow(workflowData);
      }
      
      console.log(`✅ Agent 3 criado com sucesso!`);
      console.log(`📋 Workflow ID: ${result.id}`);
      console.log(`⚠️ Nota: Workflow não ativado automaticamente devido a dependência de credenciais Supabase`);
      
      return result;
      
    } catch (error) {
      console.error('❌ Erro na implementação do Agent 3:', error);
      throw error;
    }
  }

  // Testar o workflow
  async test() {
    try {
      console.log('🧪 Testando Agent 3...');
      
      // Dados de teste
      const testData = {
        phone: '+5511999999999',
        push_name: 'Usuário Teste'
      };
      
      console.log('📤 Dados de teste:', testData);
      console.log('✅ Teste preparado - configure credenciais Supabase primeiro');
      
    } catch (error) {
      console.error('❌ Erro no teste do Agent 3:', error);
      throw error;
    }
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  const agent = new Agent3UserRegistryAccessSubflow();
  
  agent.implement()
    .then(() => agent.test())
    .then(() => {
      console.log('🎉 Agent 3 - User Registry Access Subflow implementado com sucesso!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Falha na implementação:', error);
      process.exit(1);
    });
}

module.exports = Agent3UserRegistryAccessSubflow;

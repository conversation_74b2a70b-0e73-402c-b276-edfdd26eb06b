#!/usr/bin/env node

/**
 * Edit Agent 1 - Unified User Pipeline
 * 
 * Edita especificamente o workflow existente #ybV3msRSWJcVjxoA
 * conforme as especificações do documento remote_agents_unificados.md
 * 
 * IMPORTANTE: NÃO criar novas credenciais - usar as existentes
 */

require('dotenv').config();
const N8nApiClient = require('../maintenance/n8n_api_client.js');

// Configuration
const CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL,
  n8nApiKey: process.env.N8N_API_KEY
};

// Workflow específico para editar
const WORKFLOW_ID = 'ybV3msRSWJcVjxoA';
const WORKFLOW_NAME = '01_Unified_User_Pipeline';

class Agent1Editor {
  constructor() {
    this.client = new N8nApiClient(CONFIG.n8nApiUrl, CONFIG.n8nApiKey);
  }

  // Criar estrutura do workflow conforme especificações
  createWorkflowStructure() {
    return {
      name: WORKFLOW_NAME,
      settings: {
        executionOrder: 'v1'
      },
      nodes: [
        // 1. Webhook Trigger - Único webhook do sistema (Evolution API)
        {
          id: 'webhook-trigger',
          name: 'WhatsApp Webhook',
          type: 'n8n-nodes-base.webhook',
          position: [100, 300],
          parameters: {
            path: 'whatsapp-webhook',
            httpMethod: 'POST',
            responseMode: 'responseNode'
          },
          webhookId: 'whatsapp-webhook'
        },

        // 2. Execute Workflow Node 1 - Normalize_Extract_Subflow
        {
          id: 'execute-normalize',
          name: 'Execute Normalize Extract',
          type: 'n8n-nodes-base.executeWorkflow',
          position: [300, 200],
          parameters: {
            workflowId: '9bMvISktOxQkzwtj',
            source: 'database'
          }
        },

        // 3. Execute Workflow Node 2 - User_Registry_Access_Subflow
        {
          id: 'execute-user-registry',
          name: 'Execute User Registry Access',
          type: 'n8n-nodes-base.executeWorkflow',
          position: [500, 200],
          parameters: {
            workflowId: 'QmdVie0kPrUCx5VL',
            source: 'database'
          }
        },

        // 4. Execute Workflow Node 3 - Session_Manager_Subflow
        {
          id: 'execute-session-manager',
          name: 'Execute Session Manager',
          type: 'n8n-nodes-base.executeWorkflow',
          position: [700, 200],
          parameters: {
            workflowId: 'BfbQRJX6Wu1Dig5v',
            source: 'database'
          }
        },

        // 5. Execute Workflow Node 4 - Onboarding_Orchestrator_Subflow
        {
          id: 'execute-onboarding',
          name: 'Execute Onboarding Orchestrator',
          type: 'n8n-nodes-base.executeWorkflow',
          position: [900, 200],
          parameters: {
            workflowId: '3gTFrHtPzU9q6N0d',
            source: 'database'
          }
        },

        // 6. Execute Workflow Node 5 - Message_Processor_Subflow
        {
          id: 'execute-message-processor',
          name: 'Execute Message Processor',
          type: 'n8n-nodes-base.executeWorkflow',
          position: [1100, 200],
          parameters: {
            workflowId: 'XNJfepWrkBExfkcF',
            source: 'database'
          }
        },

        // 7. Execute Workflow Node 6 - Persistence_Memory_Subflow
        {
          id: 'execute-persistence',
          name: 'Execute Persistence Memory',
          type: 'n8n-nodes-base.executeWorkflow',
          position: [1300, 200],
          parameters: {
            workflowId: 'x0cXaiDeMsYmAs9E',
            source: 'database'
          }
        },

        // 8. Webhook Response - Retorna status 200
        {
          id: 'webhook-response',
          name: 'Webhook Response',
          type: 'n8n-nodes-base.respondToWebhook',
          position: [1500, 300],
          parameters: {
            respondWith: 'json',
            responseBody: '{\n  "status": "success",\n  "message": "Mensagem processada com sucesso",\n  "timestamp": "{{ new Date().toISOString() }}"\n}',
            options: {
              responseCode: 200
            }
          }
        }
      ],
      connections: {
        'WhatsApp Webhook': {
          main: [
            [
              {
                node: 'Execute Normalize Extract',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Execute Normalize Extract': {
          main: [
            [
              {
                node: 'Execute User Registry Access',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Execute User Registry Access': {
          main: [
            [
              {
                node: 'Execute Session Manager',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Execute Session Manager': {
          main: [
            [
              {
                node: 'Execute Onboarding Orchestrator',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Execute Onboarding Orchestrator': {
          main: [
            [
              {
                node: 'Execute Message Processor',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Execute Message Processor': {
          main: [
            [
              {
                node: 'Execute Persistence Memory',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Execute Persistence Memory': {
          main: [
            [
              {
                node: 'Webhook Response',
                type: 'main',
                index: 0
              }
            ]
          ]
        }
      }
    };
  }

  // Editar o workflow existente
  async editWorkflow() {
    try {
      console.log(`🚀 Editando Agent 1 - Unified User Pipeline...`);
      console.log(`📝 Workflow ID: ${WORKFLOW_ID}`);
      console.log(`📝 Workflow Name: ${WORKFLOW_NAME}`);

      // Verificar se o workflow existe
      const existingWorkflow = await this.client.getWorkflow(WORKFLOW_ID);
      if (!existingWorkflow) {
        throw new Error(`Workflow ${WORKFLOW_ID} não encontrado!`);
      }

      console.log(`✅ Workflow encontrado: ${existingWorkflow.name}`);

      // Criar nova estrutura
      const workflowData = this.createWorkflowStructure();
      
      console.log(`📤 Atualizando workflow "${WORKFLOW_NAME}" com nova estrutura...`);
      
      // Atualizar o workflow
      const result = await this.client.updateWorkflow(WORKFLOW_ID, workflowData);
      
      console.log(`✅ Agent 1 atualizado com sucesso!`);
      console.log(`📋 Workflow ID: ${WORKFLOW_ID}`);
      console.log(`📋 Workflow Name: ${WORKFLOW_NAME}`);
      console.log(`🎯 Estrutura: Webhook → 6 Execute Workflow Nodes → Response`);
      
      return result;

    } catch (error) {
      console.error('❌ Erro ao editar Agent 1:', error.message);
      throw error;
    }
  }
}

// Executar se chamado diretamente
async function main() {
  if (!CONFIG.n8nApiKey) {
    console.error('❌ N8N_API_KEY environment variable is required');
    process.exit(1);
  }

  const editor = new Agent1Editor();
  
  try {
    await editor.editWorkflow();
    console.log('\n🎯 Agent 1 editado com sucesso conforme especificações!');
    console.log('📋 Estrutura implementada:');
    console.log('   1. Webhook Trigger (único do sistema)');
    console.log('   2. Execute Normalize Extract Subflow');
    console.log('   3. Execute User Registry Access Subflow');
    console.log('   4. Execute Session Manager Subflow');
    console.log('   5. Execute Onboarding Orchestrator Subflow');
    console.log('   6. Execute Message Processor Subflow');
    console.log('   7. Execute Persistence Memory Subflow');
    console.log('   8. Webhook Response (status 200)');
    process.exit(0);
  } catch (error) {
    console.error('\n❌ Falha ao editar Agent 1:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = Agent1Editor;

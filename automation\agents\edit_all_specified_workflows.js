#!/usr/bin/env node

/**
 * Edit All Specified Workflows
 * 
 * Edita especificamente os workflows solicitados pelo usuário:
 * - 01_Unified_User_Pipeline (#ybV3msRSWJcVjxoA)
 * - 02_Normalize_Extract_Subflow (#9bMvISktOxQkzwtj)
 * - 03_User_Registry_Access_Subflow (#QmdVie0kPrUCx5VL)
 * 
 * IMPORTANTE: NÃO criar novas credenciais - usar as existentes
 */

require('dotenv').config();

const Agent1Editor = require('./edit_agent_1_unified_pipeline.js');
const Agent2Editor = require('./edit_agent_2_normalize_extract.js');
const Agent3Editor = require('./edit_agent_3_user_registry_access.js');

// Configuration
const CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL,
  n8nApiKey: process.env.N8N_API_KEY
};

class WorkflowEditor {
  constructor() {
    this.agent1 = new Agent1Editor();
    this.agent2 = new Agent2Editor();
    this.agent3 = new Agent3Editor();
  }

  async editAllWorkflows() {
    console.log('🚀 Iniciando edição de todos os workflows especificados...\n');

    const results = {
      agent1: null,
      agent2: null,
      agent3: null,
      errors: []
    };

    try {
      // Editar Agent 1 - Unified User Pipeline
      console.log('📝 1/3 - Editando Agent 1 (Unified User Pipeline)...');
      results.agent1 = await this.agent1.editWorkflow();
      console.log('✅ Agent 1 editado com sucesso!\n');
    } catch (error) {
      console.error('❌ Erro ao editar Agent 1:', error.message);
      results.errors.push({ agent: 'Agent 1', error: error.message });
    }

    try {
      // Editar Agent 2 - Normalize Extract Subflow
      console.log('📝 2/3 - Editando Agent 2 (Normalize Extract Subflow)...');
      results.agent2 = await this.agent2.editWorkflow();
      console.log('✅ Agent 2 editado com sucesso!\n');
    } catch (error) {
      console.error('❌ Erro ao editar Agent 2:', error.message);
      results.errors.push({ agent: 'Agent 2', error: error.message });
    }

    try {
      // Editar Agent 3 - User Registry Access Subflow
      console.log('📝 3/3 - Editando Agent 3 (User Registry Access Subflow)...');
      results.agent3 = await this.agent3.editWorkflow();
      console.log('✅ Agent 3 editado com sucesso!\n');
    } catch (error) {
      console.error('❌ Erro ao editar Agent 3:', error.message);
      results.errors.push({ agent: 'Agent 3', error: error.message });
    }

    return results;
  }

  generateReport(results) {
    console.log('📊 RELATÓRIO DE EDIÇÃO DOS WORKFLOWS\n');
    console.log('=' .repeat(50));

    // Workflows editados com sucesso
    const successCount = [results.agent1, results.agent2, results.agent3].filter(Boolean).length;
    console.log(`✅ Workflows editados com sucesso: ${successCount}/3`);

    if (results.agent1) {
      console.log('   ✓ Agent 1 - Unified User Pipeline (#ybV3msRSWJcVjxoA)');
      console.log('     - Webhook único do sistema implementado');
      console.log('     - 6 Execute Workflow Nodes configurados');
      console.log('     - Webhook Response implementado');
    }

    if (results.agent2) {
      console.log('   ✓ Agent 2 - Normalize Extract Subflow (#9bMvISktOxQkzwtj)');
      console.log('     - Normalização de CPF, phone e mensagem');
      console.log('     - Validação de dados implementada');
      console.log('     - Estrutura padronizada de saída');
    }

    if (results.agent3) {
      console.log('   ✓ Agent 3 - User Registry Access Subflow (#QmdVie0kPrUCx5VL)');
      console.log('     - Query Postgres complexa implementada');
      console.log('     - Lógica IF para verificar usuário existente');
      console.log('     - Criação de novos usuários com gamificação');
      console.log('     - Determinação de access_level e permissions');
    }

    // Erros encontrados
    if (results.errors.length > 0) {
      console.log(`\n❌ Erros encontrados: ${results.errors.length}`);
      results.errors.forEach(error => {
        console.log(`   ✗ ${error.agent}: ${error.error}`);
      });
    }

    console.log('\n' + '=' .repeat(50));

    // Status final
    if (results.errors.length === 0) {
      console.log('🎯 TODOS OS WORKFLOWS FORAM EDITADOS COM SUCESSO!');
      console.log('\n📋 Próximos passos:');
      console.log('   1. Verificar workflows no n8n interface');
      console.log('   2. Ativar workflows se necessário');
      console.log('   3. Testar fluxo completo com webhook');
      console.log('   4. Configurar credenciais se houver problemas');
    } else {
      console.log('⚠️  ALGUNS WORKFLOWS APRESENTARAM PROBLEMAS');
      console.log('   Verifique os erros acima e corrija conforme necessário');
    }

    return successCount === 3;
  }
}

// Executar se chamado diretamente
async function main() {
  if (!CONFIG.n8nApiKey) {
    console.error('❌ N8N_API_KEY environment variable is required');
    process.exit(1);
  }

  const editor = new WorkflowEditor();
  
  try {
    const results = await editor.editAllWorkflows();
    const allSuccess = editor.generateReport(results);
    
    process.exit(allSuccess ? 0 : 1);
  } catch (error) {
    console.error('\n❌ Falha geral na edição dos workflows:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = WorkflowEditor;

#!/usr/bin/env node

/**
 * Test Agents Integration
 * 
 * Testa a integração entre os agentes 1, 2 e 3
 * Simula uma mensagem WhatsApp completa através do sistema
 */

require('dotenv').config();
const N8nApiClient = require('../maintenance/n8n_api_client.js');

// Configuração
const CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL || 'https://n8n-n8n.w9jo16.easypanel.host',
  n8nApiKey: process.env.N8N_API_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhcGkiLCJpYXQiOjE3NTYyMjEyMTEsImV4cCI6MTc1ODc3MjgwMH0.6BzCKbfcBOMtPukrVHFN1Mq2B4Fe8QezboF7CRp323M'
};

class AgentsIntegrationTester {
  constructor() {
    this.client = new N8nApiClient(CONFIG.n8nApiUrl, CONFIG.n8nApiKey);
  }

  // Verificar status dos workflows
  async checkWorkflowsStatus() {
    try {
      console.log('🔍 Verificando status dos workflows dos agentes...');
      
      const agentWorkflows = [
        'Unified_User_Pipeline',
        'Normalize_Extract_Subflow', 
        'User_Registry_Access_Subflow'
      ];
      
      const status = {};
      
      for (const workflowName of agentWorkflows) {
        const workflow = await this.client.findWorkflowByName(workflowName);
        if (workflow) {
          status[workflowName] = {
            id: workflow.id,
            active: workflow.active,
            found: true
          };
          console.log(`✅ ${workflowName}: ID ${workflow.id}, Ativo: ${workflow.active}`);
        } else {
          status[workflowName] = {
            found: false
          };
          console.log(`❌ ${workflowName}: Não encontrado`);
        }
      }
      
      return status;
      
    } catch (error) {
      console.error('❌ Erro ao verificar status:', error);
      throw error;
    }
  }

  // Testar webhook do Agent 1
  async testWebhook() {
    try {
      console.log('🧪 Testando webhook do Agent 1...');
      
      // Dados de teste simulando WhatsApp
      const testData = {
        from: '5511999999999',
        body: 'Olá! Esta é uma mensagem de teste do sistema AI Comtxae.',
        pushName: 'Usuário Teste',
        timestamp: Date.now(),
        messageType: 'text'
      };
      
      const webhookUrl = `${CONFIG.n8nApiUrl}/webhook/whatsapp-webhook`;
      
      console.log('📤 Enviando dados para webhook...');
      console.log('🌐 URL:', webhookUrl);
      console.log('📋 Dados:', JSON.stringify(testData, null, 2));
      
      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testData)
      });
      
      console.log('📥 Status da resposta:', response.status);
      
      if (response.ok) {
        const responseData = await response.json();
        console.log('✅ Webhook respondeu com sucesso!');
        console.log('📋 Resposta:', JSON.stringify(responseData, null, 2));
      } else {
        const errorText = await response.text();
        console.log('❌ Erro na resposta do webhook:', errorText);
      }
      
      return response.ok;
      
    } catch (error) {
      console.error('❌ Erro no teste do webhook:', error);
      return false;
    }
  }

  // Verificar execuções recentes
  async checkRecentExecutions() {
    try {
      console.log('📊 Verificando execuções recentes...');
      
      const executions = await this.client.getExecutions();
      
      // Filtrar execuções dos últimos 5 minutos
      const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
      const recentExecutions = executions.data.filter(exec => 
        new Date(exec.startedAt).getTime() > fiveMinutesAgo
      );
      
      console.log(`📈 Execuções recentes (últimos 5 min): ${recentExecutions.length}`);
      
      recentExecutions.forEach(exec => {
        console.log(`- ${exec.workflowName}: ${exec.status} (${exec.startedAt})`);
      });
      
      return recentExecutions;
      
    } catch (error) {
      console.error('❌ Erro ao verificar execuções:', error);
      return [];
    }
  }

  // Executar teste completo
  async runFullTest() {
    try {
      console.log('🚀 Iniciando teste completo de integração dos agentes...\n');
      
      // 1. Verificar status dos workflows
      console.log('=== ETAPA 1: VERIFICAÇÃO DE STATUS ===');
      const status = await this.checkWorkflowsStatus();
      console.log('');
      
      // 2. Testar webhook
      console.log('=== ETAPA 2: TESTE DO WEBHOOK ===');
      const webhookSuccess = await this.testWebhook();
      console.log('');
      
      // 3. Aguardar um pouco para as execuções processarem
      console.log('⏳ Aguardando processamento (10 segundos)...');
      await new Promise(resolve => setTimeout(resolve, 10000));
      
      // 4. Verificar execuções
      console.log('=== ETAPA 3: VERIFICAÇÃO DE EXECUÇÕES ===');
      const executions = await this.checkRecentExecutions();
      console.log('');
      
      // 5. Relatório final
      console.log('=== RELATÓRIO FINAL ===');
      console.log('📊 Status dos Workflows:');
      Object.entries(status).forEach(([name, info]) => {
        if (info.found) {
          console.log(`  ✅ ${name}: Encontrado e ${info.active ? 'Ativo' : 'Inativo'}`);
        } else {
          console.log(`  ❌ ${name}: Não encontrado`);
        }
      });
      
      console.log(`🌐 Teste do Webhook: ${webhookSuccess ? 'Sucesso' : 'Falha'}`);
      console.log(`📈 Execuções Recentes: ${executions.length}`);
      
      const allWorkflowsFound = Object.values(status).every(s => s.found);
      const allWorkflowsActive = Object.values(status).every(s => s.found && s.active);
      
      if (allWorkflowsFound && allWorkflowsActive && webhookSuccess) {
        console.log('🎉 TESTE COMPLETO: SUCESSO! Todos os agentes estão funcionando.');
      } else {
        console.log('⚠️ TESTE COMPLETO: PARCIAL. Alguns problemas foram identificados.');
      }
      
    } catch (error) {
      console.error('💥 Erro no teste completo:', error);
    }
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  const tester = new AgentsIntegrationTester();
  
  tester.runFullTest()
    .then(() => {
      console.log('\n✅ Teste de integração concluído!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Falha no teste:', error);
      process.exit(1);
    });
}

module.exports = AgentsIntegrationTester;

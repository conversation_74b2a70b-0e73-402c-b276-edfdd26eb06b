#!/usr/bin/env node

/**
 * Script para analisar detalhadamente o workflow Unified_User_Pipeline
 * e identificar o que precisa ser implementado ou corrigido
 */

require('dotenv').config();
const N8nApiClient = require('./automation/maintenance/n8n_api_client.js');

// Configuration
const CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL || 'https://n8n-n8n.w9jo16.easypanel.host/',
  n8nApiKey: process.env.N8N_API_KEY
};

async function analyzeUnifiedPipeline() {
  console.log('🔍 ANÁLISE DETALHADA - UNIFIED_USER_PIPELINE');
  console.log('='.repeat(60));
  console.log('');

  try {
    const client = new N8nApiClient(CONFIG.n8nApiUrl, CONFIG.n8nApiKey);
    
    // Buscar o workflow
    const workflows = await client.getWorkflows();
    const workflowList = workflows.data || workflows;
    const pipeline = workflowList.find(w => w.name === '01_Unified_User_Pipeline' || w.id === 'ybV3msRSWJcVjxoA');
    
    if (!pipeline) {
      console.log('❌ Workflow não encontrado!');
      return;
    }

    console.log('📊 INFORMAÇÕES BÁSICAS:');
    console.log(`   - Nome: ${pipeline.name}`);
    console.log(`   - ID: ${pipeline.id}`);
    console.log(`   - Status: ${pipeline.active ? 'ATIVO' : 'INATIVO'}`);
    console.log(`   - Criado em: ${pipeline.createdAt}`);
    console.log(`   - Atualizado em: ${pipeline.updatedAt}`);
    console.log('');

    // Obter detalhes completos
    const details = await client.getWorkflow(pipeline.id);
    
    console.log('🏗️ ESTRUTURA DE NÓS:');
    console.log(`   Total de nós: ${details.nodes.length}`);
    console.log('');

    // Analisar cada nó
    details.nodes.forEach((node, index) => {
      console.log(`${index + 1}. ${node.name}`);
      console.log(`   - Tipo: ${node.type}`);
      console.log(`   - Posição: (${node.position[0]}, ${node.position[1]})`);
      
      // Analisar parâmetros específicos
      if (node.type === 'n8n-nodes-base.webhook') {
        console.log('   - 🌐 WEBHOOK CONFIGURADO:');
        console.log(`     • Método: ${node.parameters?.httpMethod || 'POST'}`);
        console.log(`     • Path: ${node.parameters?.path || 'webhook'}`);
        console.log(`     • Autenticação: ${node.parameters?.authentication || 'none'}`);
      }
      
      if (node.type === 'n8n-nodes-base.code') {
        console.log('   - 💻 CÓDIGO:');
        const codeLength = node.parameters?.jsCode?.length || 0;
        console.log(`     • Tamanho do código: ${codeLength} caracteres`);
        if (codeLength > 0) {
          const codePreview = node.parameters.jsCode.substring(0, 100).replace(/\n/g, ' ');
          console.log(`     • Preview: ${codePreview}...`);
        } else {
          console.log('     • ⚠️ CÓDIGO VAZIO!');
        }
      }
      
      if (node.type === 'n8n-nodes-base.switch') {
        console.log('   - 🔀 SWITCH:');
        const rules = node.parameters?.rules?.values || [];
        console.log(`     • Número de regras: ${rules.length}`);
        rules.forEach((rule, rIndex) => {
          console.log(`     • Regra ${rIndex + 1}: ${rule.conditions?.string?.[0]?.value1 || 'N/A'}`);
        });
      }
      
      if (node.type === 'n8n-nodes-base.executeWorkflow') {
        console.log('   - ⚡ EXECUTE WORKFLOW:');
        console.log(`     • Workflow: ${node.parameters?.workflowId || 'N/A'}`);
        console.log(`     • Modo: ${node.parameters?.mode || 'own'}`);
      }
      
      console.log('');
    });

    // Analisar conexões
    console.log('🔗 CONEXÕES ENTRE NÓS:');
    if (details.connections && Object.keys(details.connections).length > 0) {
      Object.entries(details.connections).forEach(([nodeName, connections]) => {
        console.log(`   ${nodeName}:`);
        Object.entries(connections).forEach(([outputIndex, outputs]) => {
          outputs.forEach((output, outIndex) => {
            console.log(`     → ${output.node} (entrada ${output.index})`);
          });
        });
      });
    } else {
      console.log('   ⚠️ NENHUMA CONEXÃO ENCONTRADA!');
    }
    console.log('');

    // Verificar conformidade com especificação
    console.log('✅ VERIFICAÇÃO DE CONFORMIDADE:');
    
    const hasWebhook = details.nodes.some(n => n.type === 'n8n-nodes-base.webhook');
    const hasCodeNode = details.nodes.some(n => n.type === 'n8n-nodes-base.code');
    const hasSwitchNode = details.nodes.some(n => n.type === 'n8n-nodes-base.switch');
    const executeWorkflowNodes = details.nodes.filter(n => n.type === 'n8n-nodes-base.executeWorkflow');
    
    console.log(`   - Webhook Trigger: ${hasWebhook ? '✅' : '❌'}`);
    console.log(`   - Code Node (extração): ${hasCodeNode ? '✅' : '❌'}`);
    console.log(`   - Switch Node (roteamento): ${hasSwitchNode ? '✅' : '❌'}`);
    console.log(`   - Execute Workflow Nodes: ${executeWorkflowNodes.length}/6 ${executeWorkflowNodes.length === 6 ? '✅' : '⚠️'}`);
    
    console.log('');
    console.log('📋 SUBFLUXOS CONECTADOS:');
    executeWorkflowNodes.forEach((node, index) => {
      const workflowId = node.parameters?.workflowId;
      console.log(`   ${index + 1}. ${node.name} → Workflow ID: ${workflowId || 'NÃO CONFIGURADO'}`);
    });

    console.log('');
    console.log('🎯 RECOMENDAÇÕES:');
    
    if (!hasCodeNode) {
      console.log('   ❌ CRÍTICO: Adicionar Code Node para extração de CPF, phone, access_level');
    }
    
    if (!hasSwitchNode) {
      console.log('   ❌ CRÍTICO: Adicionar Switch Node para roteamento por access_level (1-7)');
    }
    
    if (executeWorkflowNodes.length !== 6) {
      console.log(`   ⚠️ ATENÇÃO: Esperados 6 Execute Workflow nodes, encontrados ${executeWorkflowNodes.length}`);
    }
    
    // Verificar se os Execute Workflow nodes têm IDs configurados
    const unconfiguredNodes = executeWorkflowNodes.filter(n => !n.parameters?.workflowId);
    if (unconfiguredNodes.length > 0) {
      console.log(`   ⚠️ ATENÇÃO: ${unconfiguredNodes.length} Execute Workflow nodes sem workflow configurado`);
    }

  } catch (error) {
    console.error('❌ Erro na análise:', error.message);
  }
}

// Executar análise
analyzeUnifiedPipeline().catch(console.error);

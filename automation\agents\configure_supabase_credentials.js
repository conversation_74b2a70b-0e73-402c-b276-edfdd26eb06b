#!/usr/bin/env node

/**
 * Configure Supabase Credentials
 * 
 * Configura as credenciais do Supabase no n8n para os workflows funcionarem
 */

require('dotenv').config();
const N8nApiClient = require('../maintenance/n8n_api_client.js');

// Configuração
const CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL || 'https://n8n-n8n.w9jo16.easypanel.host',
  n8nApiKey: process.env.N8N_API_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhcGkiLCJpYXQiOjE3NTYyMjEyMTEsImV4cCI6MTc1ODc3MjgwMH0.6BzCKbfcBOMtPukrVHFN1Mq2B4Fe8QezboF7CRp323M',
  supabaseUrl: 'https://qvkstxeayyzrntywifdi.supabase.co',
  supabaseKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF2a3N0eGVheXl6cm50eXdpZmRpIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNzM5NzI2NCwiZXhwIjoyMDUyOTczMjY0fQ.i8KnLbb6utU4LntRKt_pxg_Hc8OcMQS'
};

class SupabaseCredentialsConfigurator {
  constructor() {
    this.client = new N8nApiClient(CONFIG.n8nApiUrl, CONFIG.n8nApiKey);
  }

  // Configurar credenciais do Supabase
  async configureCredentials() {
    try {
      console.log('🔑 Configurando credenciais do Supabase...');
      
      // Dados das credenciais
      const credentialData = {
        name: 'Supabase API',
        type: 'supabaseApi',
        data: {
          host: CONFIG.supabaseUrl,
          serviceRole: CONFIG.supabaseKey
        }
      };
      
      console.log('📤 Criando credencial Supabase...');
      console.log('🌐 URL:', CONFIG.supabaseUrl);
      console.log('🔑 Service Key:', CONFIG.supabaseKey.substring(0, 20) + '...');
      
      // Tentar criar a credencial
      const result = await this.client.createCredential(credentialData);
      
      console.log('✅ Credencial Supabase criada com sucesso!');
      console.log('📋 Credential ID:', result.id);
      
      return result;
      
    } catch (error) {
      console.error('❌ Erro ao configurar credenciais:', error);
      
      // Se a credencial já existe, tentar listar as existentes
      if (error.message.includes('already exists') || error.message.includes('duplicate')) {
        console.log('ℹ️ Credencial pode já existir. Listando credenciais...');
        try {
          const credentials = await this.client.getCredentials();
          const supabaseCredentials = credentials.filter(cred => 
            cred.type === 'supabaseApi' || cred.name.includes('Supabase')
          );
          
          console.log('📋 Credenciais Supabase encontradas:');
          supabaseCredentials.forEach(cred => {
            console.log(`- ${cred.name} (ID: ${cred.id}) - Tipo: ${cred.type}`);
          });
          
        } catch (listError) {
          console.error('❌ Erro ao listar credenciais:', listError);
        }
      }
      
      throw error;
    }
  }

  // Testar conexão com Supabase
  async testConnection() {
    try {
      console.log('🧪 Testando conexão com Supabase...');
      
      // Fazer uma requisição simples para testar
      const response = await fetch(`${CONFIG.supabaseUrl}/rest/v1/users?select=count`, {
        method: 'GET',
        headers: {
          'apikey': CONFIG.supabaseKey,
          'Authorization': `Bearer ${CONFIG.supabaseKey}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        console.log('✅ Conexão com Supabase funcionando!');
        const data = await response.text();
        console.log('📊 Resposta:', data.substring(0, 100) + '...');
      } else {
        console.log('⚠️ Resposta não OK:', response.status, response.statusText);
      }
      
    } catch (error) {
      console.error('❌ Erro no teste de conexão:', error);
    }
  }

  // Ativar workflows que dependem do Supabase
  async activateWorkflows() {
    try {
      console.log('🔄 Tentando ativar workflows que dependem do Supabase...');
      
      const workflowNames = [
        'User_Registry_Access_Subflow',
        'Normalize_Extract_Subflow'
      ];
      
      for (const workflowName of workflowNames) {
        try {
          const workflow = await this.client.findWorkflowByName(workflowName);
          if (workflow) {
            console.log(`🔄 Ativando ${workflowName}...`);
            await this.client.activateWorkflow(workflow.id);
            console.log(`✅ ${workflowName} ativado com sucesso!`);
          } else {
            console.log(`⚠️ Workflow ${workflowName} não encontrado`);
          }
        } catch (error) {
          console.log(`❌ Erro ao ativar ${workflowName}: ${error.message}`);
        }
      }
      
    } catch (error) {
      console.error('❌ Erro ao ativar workflows:', error);
    }
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  const configurator = new SupabaseCredentialsConfigurator();
  
  configurator.testConnection()
    .then(() => configurator.configureCredentials())
    .then(() => configurator.activateWorkflows())
    .then(() => {
      console.log('🎉 Configuração do Supabase concluída!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Falha na configuração:', error);
      process.exit(1);
    });
}

module.exports = SupabaseCredentialsConfigurator;

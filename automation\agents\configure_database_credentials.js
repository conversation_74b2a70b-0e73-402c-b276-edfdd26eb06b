#!/usr/bin/env node

/**
 * Configure Database Credentials
 * 
 * Configura as credenciais do PostgreSQL e Supabase no n8n para os workflows funcionarem
 */

require('dotenv').config();
const N8nApiClient = require('../maintenance/n8n_api_client.js');

// Configuração
const CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL || 'https://n8n-n8n.w9jo16.easypanel.host',
  n8nApiKey: process.env.N8N_API_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhcGkiLCJpYXQiOjE3NTYyMjEyMTEsImV4cCI6MTc1ODc3MjgwMH0.6BzCKbfcBOMtPukrVHFN1Mq2B4Fe8QezboF7CRp323M',
  
  // Supabase
  supabaseUrl: 'https://qvkstxeayyzrntywifdi.supabase.co',
  supabaseKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF2a3N0eGVheXl6cm50eXdpZmRpIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNzM5NzI2NCwiZXhwIjoyMDUyOTczMjY0fQ.i8KnLbb6utU4LntRKt_pxg_Hc8OcMQS',
  
  // PostgreSQL (via Supabase)
  postgresHost: 'aws-0-sa-east-1.pooler.supabase.com',
  postgresPort: 6543,
  postgresDatabase: 'postgres',
  postgresUser: 'postgres.qvkstxeayyzrntywifdi',
  postgresPassword: 'i8KnLbb6utU4LntRKt_pxg_Hc8OcMQS'
};

class DatabaseCredentialsConfigurator {
  constructor() {
    this.client = new N8nApiClient(CONFIG.n8nApiUrl, CONFIG.n8nApiKey);
  }

  // Configurar credenciais do Supabase
  async configureSupabaseCredentials() {
    try {
      console.log('🔑 Configurando credenciais do Supabase...');
      
      const credentialData = {
        name: 'Supabase API',
        type: 'supabaseApi',
        data: {
          host: CONFIG.supabaseUrl,
          serviceRole: CONFIG.supabaseKey
        }
      };
      
      console.log('📤 Criando credencial Supabase...');
      console.log('🌐 URL:', CONFIG.supabaseUrl);
      
      const result = await this.client.createCredential(credentialData);
      console.log('✅ Credencial Supabase criada com sucesso!');
      console.log('📋 Credential ID:', result.id);
      
      return result;
      
    } catch (error) {
      if (error.message.includes('already exists') || error.message.includes('duplicate')) {
        console.log('ℹ️ Credencial Supabase já existe');
        return { status: 'exists' };
      }
      throw error;
    }
  }

  // Configurar credenciais do PostgreSQL
  async configurePostgresCredentials() {
    try {
      console.log('🔑 Configurando credenciais do PostgreSQL...');
      
      const credentialData = {
        name: 'PostgreSQL',
        type: 'postgres',
        data: {
          host: CONFIG.postgresHost,
          port: CONFIG.postgresPort,
          database: CONFIG.postgresDatabase,
          user: CONFIG.postgresUser,
          password: CONFIG.postgresPassword,
          ssl: {
            mode: 'require'
          }
        }
      };
      
      console.log('📤 Criando credencial PostgreSQL...');
      console.log('🌐 Host:', CONFIG.postgresHost);
      console.log('🗄️ Database:', CONFIG.postgresDatabase);
      console.log('👤 User:', CONFIG.postgresUser);
      
      const result = await this.client.createCredential(credentialData);
      console.log('✅ Credencial PostgreSQL criada com sucesso!');
      console.log('📋 Credential ID:', result.id);
      
      return result;
      
    } catch (error) {
      if (error.message.includes('already exists') || error.message.includes('duplicate')) {
        console.log('ℹ️ Credencial PostgreSQL já existe');
        return { status: 'exists' };
      }
      throw error;
    }
  }

  // Testar conexão PostgreSQL
  async testPostgresConnection() {
    try {
      console.log('🧪 Testando conexão PostgreSQL...');
      
      // Usar pg para testar conexão diretamente
      const { Client } = require('pg');
      
      const client = new Client({
        host: CONFIG.postgresHost,
        port: CONFIG.postgresPort,
        database: CONFIG.postgresDatabase,
        user: CONFIG.postgresUser,
        password: CONFIG.postgresPassword,
        ssl: { rejectUnauthorized: false }
      });
      
      await client.connect();
      
      // Testar query simples
      const result = await client.query('SELECT COUNT(*) FROM users');
      console.log('✅ Conexão PostgreSQL funcionando!');
      console.log('📊 Usuários na tabela:', result.rows[0].count);
      
      await client.end();
      
    } catch (error) {
      console.error('❌ Erro no teste PostgreSQL:', error.message);
    }
  }

  // Testar conexão Supabase
  async testSupabaseConnection() {
    try {
      console.log('🧪 Testando conexão Supabase...');
      
      const response = await fetch(`${CONFIG.supabaseUrl}/rest/v1/users?select=count`, {
        method: 'GET',
        headers: {
          'apikey': CONFIG.supabaseKey,
          'Authorization': `Bearer ${CONFIG.supabaseKey}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        console.log('✅ Conexão Supabase funcionando!');
        const data = await response.text();
        console.log('📊 Resposta:', data.substring(0, 100) + '...');
      } else {
        console.log('⚠️ Resposta Supabase não OK:', response.status, response.statusText);
      }
      
    } catch (error) {
      console.error('❌ Erro no teste Supabase:', error.message);
    }
  }

  // Ativar workflows que dependem das credenciais
  async activateWorkflows() {
    try {
      console.log('🔄 Tentando ativar workflows...');
      
      const workflowNames = [
        'User_Registry_Access_Subflow',
        'Normalize_Extract_Subflow'
      ];
      
      for (const workflowName of workflowNames) {
        try {
          const workflow = await this.client.findWorkflowByName(workflowName);
          if (workflow && !workflow.active) {
            console.log(`🔄 Ativando ${workflowName}...`);
            await this.client.activateWorkflow(workflow.id);
            console.log(`✅ ${workflowName} ativado com sucesso!`);
          } else if (workflow && workflow.active) {
            console.log(`ℹ️ ${workflowName} já está ativo`);
          } else {
            console.log(`⚠️ Workflow ${workflowName} não encontrado`);
          }
        } catch (error) {
          console.log(`❌ Erro ao ativar ${workflowName}: ${error.message}`);
        }
      }
      
    } catch (error) {
      console.error('❌ Erro ao ativar workflows:', error);
    }
  }

  // Executar configuração completa
  async configureAll() {
    try {
      console.log('🚀 Iniciando configuração completa das credenciais...\n');
      
      // 1. Testar conexões
      console.log('=== ETAPA 1: TESTE DE CONEXÕES ===');
      await this.testSupabaseConnection();
      await this.testPostgresConnection();
      console.log('');
      
      // 2. Configurar credenciais
      console.log('=== ETAPA 2: CONFIGURAÇÃO DE CREDENCIAIS ===');
      await this.configureSupabaseCredentials();
      await this.configurePostgresCredentials();
      console.log('');
      
      // 3. Ativar workflows
      console.log('=== ETAPA 3: ATIVAÇÃO DE WORKFLOWS ===');
      await this.activateWorkflows();
      console.log('');
      
      console.log('🎉 Configuração completa finalizada!');
      
    } catch (error) {
      console.error('💥 Erro na configuração:', error);
      throw error;
    }
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  const configurator = new DatabaseCredentialsConfigurator();
  
  configurator.configureAll()
    .then(() => {
      console.log('✅ Configuração de credenciais concluída!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Falha na configuração:', error);
      process.exit(1);
    });
}

module.exports = DatabaseCredentialsConfigurator;

{"name": "WhatsApp_AI_Assistant_Unified", "active": true, "nodes": [{"parameters": {"httpMethod": "POST", "path": "whatsapp-unified", "options": {}}, "id": "whatsapp-webhook", "name": "WhatsApp Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [100, 300], "webhookId": "whatsapp-unified-webhook"}, {"parameters": {"jsCode": "// Extrair e normalizar dados do WhatsApp\nconst webhookData = $input.all()[0].json;\n\n// Extrair dados principais\nconst from = webhookData.data?.key?.remoteJid || webhookData.from;\nconst message = webhookData.data?.message?.conversation || \n                webhookData.data?.message?.extendedTextMessage?.text || \n                webhookData.body || '';\nconst pushName = webhookData.data?.pushName || webhookData.pushName || 'Usuário';\nconst timestamp = new Date().toISOString();\n\n// Normalizar telefone (remover caracteres especiais)\nconst phone = from.replace(/[^0-9]/g, '');\n\n// Gerar CPF temporário baseado no phone (últimos 11 dígitos)\nconst cpf = phone.length >= 11 ? phone.slice(-11) : phone.padStart(11, '0');\n\n// Dados normalizados\nconst normalizedData = {\n  cpf: cpf,\n  phone: phone,\n  message: message.trim(),\n  pushName: pushName,\n  timestamp: timestamp,\n  source: 'whatsapp',\n  raw_data: webhookData\n};\n\nconsole.log('📱 Dados normalizados:', normalizedData);\n\nreturn [{ json: normalizedData }];"}, "id": "extract-normalize", "name": "Extrair e Normalizar", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [300, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- 🎯 QUERY UNIFICADA: Usuário + Sessão + Mensagem\nWITH user_upsert AS (\n  -- Buscar usuário existente ou criar novo\n  INSERT INTO users (cpf, phone, name, access_level, active, last_access, onboarding_progress)\n  VALUES (\n    $1, \n    $2, \n    $3, \n    1, \n    true, \n    NOW(),\n    '{\"level\": 1, \"badges\": [], \"current_step\": \"welcome\", \"completed_steps\": [], \"gamification_score\": 0}'::jsonb\n  )\n  ON CONFLICT (phone) \n  DO UPDATE SET \n    last_access = NOW(),\n    name = COALESCE(users.name, $3)\n  RETURNING cpf, phone, name, access_level, onboarding_progress, active, legacy_id\n),\nsession_management AS (\n  -- <PERSON><PERSON> sessão ativa ou criar nova\n  WITH active_session AS (\n    SELECT cs.id, cs.user_id, cs.last_activity, cs.session_data\n    FROM chat_sessions cs\n    JOIN users u ON cs.user_id = u.legacy_id\n    WHERE u.phone = $2 \n      AND cs.active = true\n      AND cs.last_activity > NOW() - INTERVAL '30 minutes'\n    ORDER BY cs.last_activity DESC\n    LIMIT 1\n  )\n  SELECT \n    COALESCE(\n      (SELECT id FROM active_session),\n      (\n        INSERT INTO chat_sessions (user_id, channel_type, channel_identifier, active, last_activity, session_data)\n        SELECT \n          (SELECT legacy_id FROM user_upsert),\n          'whatsapp',\n          $2,\n          true,\n          NOW(),\n          jsonb_build_object('access_level', (SELECT access_level FROM user_upsert), 'device', 'whatsapp')\n        RETURNING id\n      ).id\n    ) as session_id,\n    COALESCE(\n      (SELECT last_activity FROM active_session),\n      NOW()\n    ) as last_activity\n),\nmessage_insert AS (\n  -- Inserir mensagem no histórico\n  INSERT INTO chat_messages (\n    session_id,\n    user_id,\n    content,\n    message_type,\n    direction,\n    metadata\n  )\n  SELECT \n    (SELECT session_id FROM session_management),\n    (SELECT legacy_id FROM user_upsert),\n    $4,\n    'text',\n    'inbound',\n    jsonb_build_object(\n      'pushName', $3,\n      'timestamp', $5,\n      'source', 'whatsapp'\n    )\n  RETURNING id as message_id, created_at as message_timestamp\n),\ncontext_data AS (\n  -- Buscar contexto da conversa (últimas 5 mensagens)\n  SELECT \n    json_agg(\n      json_build_object(\n        'content', cm.content,\n        'direction', cm.direction,\n        'timestamp', cm.created_at\n      ) ORDER BY cm.created_at DESC\n    ) as conversation_history\n  FROM chat_messages cm\n  JOIN session_management sm ON cm.session_id = sm.session_id\n  WHERE cm.created_at >= NOW() - INTERVAL '1 hour'\n  LIMIT 5\n)\n-- Resultado final consolidado\nSELECT \n  u.cpf,\n  u.phone,\n  u.name,\n  u.access_level,\n  u.onboarding_progress,\n  sm.session_id,\n  mi.message_id,\n  mi.message_timestamp,\n  cd.conversation_history,\n  $4 as current_message,\n  'ready_for_ai' as status\nFROM user_upsert u\nCROSS JOIN session_management sm\nCROSS JOIN message_insert mi\nCROSS JOIN context_data cd;", "options": {"queryReplacement": "={{ $json.cpf }},={{ $json.phone }},={{ $json.pushName }},={{ $json.message }},={{ $json.timestamp }}"}}, "id": "process-user-session", "name": "Processar Usuário e Sessão", "type": "n8n-nodes-base.postgres", "typeVersion": 2, "position": [500, 300], "credentials": {"postgres": {"id": "tqtLhtoqDEUD7G1k", "name": "assistente_v0.1"}}}, {"parameters": {"resource": "chat", "operation": "create", "model": "gpt-4", "messages": {"values": [{"role": "system", "content": "Você é o assistente de IA da \"AI Comtxae\", uma plataforma de automação comunitária.\n\nCONTEXTO DO USUÁRIO:\n- Nome: {{ $json.name }}\n- Nível de Acesso: {{ $json.access_level }}\n- Progresso: {{ $json.onboarding_progress }}\n\nHISTÓRICO DA CONVERSA:\n{{ $json.conversation_history }}\n\nINSTRUÇÕES:\n1. Seja amigável e prestativo\n2. Use o nome do usuário quando apropriado\n3. Mantenha respostas concisas (máximo 200 caracteres para WhatsApp)\n4. Se for primeira interação, dê boas-vindas\n5. Ofereça ajuda baseada no nível de acesso do usuário\n\nResponda de forma natural e contextualizada."}, {"role": "user", "content": "={{ $json.current_message }}"}]}, "options": {"temperature": 0.7, "maxTokens": 150}}, "id": "ai-processing", "name": "Processar com IA", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [700, 300], "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI API"}}}, {"parameters": {"method": "POST", "url": "https://evolution-evolution-api.w9jo16.easypanel.host/message/sendText/AI_Comtxae", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "httpHeaderAuth": {"name": "apikey", "value": "D04C98B9077B-456A-8BCA-4C5892630893"}, "sendBody": true, "bodyContentType": "json", "jsonBody": "{\n  \"number\": \"{{ $('Processar Usuário e Sessão').item.json.phone }}\",\n  \"text\": \"{{ $('Processar com IA').item.json.choices[0].message.content }}\",\n  \"delay\": 1000\n}", "options": {}}, "id": "send-response", "name": "Enviar Resposta", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [900, 300]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Mensagem processada com sucesso\",\n  \"user_cpf\": \"{{ $('Processar Usuário e Sessão').item.json.cpf }}\",\n  \"session_id\": \"{{ $('Processar Usuário e Sessão').item.json.session_id }}\",\n  \"ai_response\": \"{{ $('Processar com IA').item.json.choices[0].message.content }}\",\n  \"timestamp\": \"{{ new Date().toISOString() }}\"\n}"}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.webhookResponse", "typeVersion": 1, "position": [1100, 300]}], "connections": {"WhatsApp Webhook": {"main": [[{"node": "Extrair e Normalizar", "type": "main", "index": 0}]]}, "Extrair e Normalizar": {"main": [[{"node": "Processar Usuário e Sessão", "type": "main", "index": 0}]]}, "Processar Usuário e Sessão": {"main": [[{"node": "Processar com IA", "type": "main", "index": 0}]]}, "Processar com IA": {"main": [[{"node": "Enviar Resposta", "type": "main", "index": 0}]]}, "Enviar Resposta": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "meta": {"templateCredsSetupCompleted": true}}
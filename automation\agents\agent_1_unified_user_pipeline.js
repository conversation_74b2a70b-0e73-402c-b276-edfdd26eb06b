#!/usr/bin/env node

/**
 * Agent 1 - Unified User Pipeline
 * 
 * Implementa o workflow principal do sistema AI Comtxae
 * - Webhook único para receber dados do WhatsApp via Evolution API
 * - Roteamento por access_level (1-7)
 * - 6 Execute Workflow nodes para subfluxos
 * 
 * Baseado em: REMOTE_AGENTS_FINAL_CONSOLIDADO.md
 */

require('dotenv').config();
const N8nApiClient = require('../maintenance/n8n_api_client.js');

// Configuração
const CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL || 'https://n8n-n8n.w9jo16.easypanel.host',
  n8nApiKey: process.env.N8N_API_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhcGkiLCJpYXQiOjE3NTYyMjEyMTEsImV4cCI6MTc1ODc3MjgwMH0.6BzCKbfcBOMtPukrVHFN1Mq2B4Fe8QezboF7CRp323M'
};

class Agent1UnifiedUserPipeline {
  constructor() {
    this.client = new N8nApiClient(CONFIG.n8nApiUrl, CONFIG.n8nApiKey);
    this.workflowName = 'Unified_User_Pipeline';
  }

  // Criar estrutura do workflow
  createWorkflowStructure() {
    return {
      name: this.workflowName,
      nodes: [
        // 1. Webhook Trigger - Entrada única do sistema
        {
          id: 'webhook-trigger',
          name: 'WhatsApp Webhook',
          type: 'n8n-nodes-base.webhook',
          typeVersion: 1,
          position: [240, 300],
          parameters: {
            httpMethod: 'POST',
            path: 'whatsapp-webhook',
            responseMode: 'responseNode',
            options: {}
          }
        },

        // 2. Code Node - Extração de dados básicos
        {
          id: 'extract-data',
          name: 'Extract Basic Data',
          type: 'n8n-nodes-base.code',
          typeVersion: 2,
          position: [460, 300],
          parameters: {
            language: 'python',
            code: `# Extração de dados básicos do webhook WhatsApp
import json

# Dados de entrada do webhook
webhook_data = items[0]['json']

# Extrair informações básicas
phone = webhook_data.get('from', '')
message_body = webhook_data.get('body', '')
push_name = webhook_data.get('pushName', '')
timestamp = webhook_data.get('timestamp', '')
message_type = webhook_data.get('messageType', 'text')

# Estruturar dados para próximos nós
extracted_data = {
    'phone': phone,
    'message_body': message_body,
    'push_name': push_name,
    'timestamp': timestamp,
    'message_type': message_type,
    'original_data': webhook_data
}

return [{'json': extracted_data}]`
          }
        },

        // 3-8. Execute Workflow Nodes - Um para cada subfluxo
        {
          id: 'execute-normalize',
          name: 'Execute Normalize Extract',
          type: 'n8n-nodes-base.executeWorkflow',
          typeVersion: 1,
          position: [680, 180],
          parameters: {
            workflowId: 'Normalize_Extract_Subflow',
            waitForExecution: true
          }
        },

        {
          id: 'execute-registry',
          name: 'Execute User Registry',
          type: 'n8n-nodes-base.executeWorkflow',
          typeVersion: 1,
          position: [680, 260],
          parameters: {
            workflowId: 'User_Registry_Access_Subflow',
            waitForExecution: true
          }
        },

        {
          id: 'execute-session',
          name: 'Execute Session Manager',
          type: 'n8n-nodes-base.executeWorkflow',
          typeVersion: 1,
          position: [680, 340],
          parameters: {
            workflowId: 'Session_Manager_Subflow',
            waitForExecution: true
          }
        },

        {
          id: 'execute-onboarding',
          name: 'Execute Onboarding',
          type: 'n8n-nodes-base.executeWorkflow',
          typeVersion: 1,
          position: [680, 420],
          parameters: {
            workflowId: 'Onboarding_Orchestrator_Subflow',
            waitForExecution: true
          }
        },

        {
          id: 'execute-processor',
          name: 'Execute Message Processor',
          type: 'n8n-nodes-base.executeWorkflow',
          typeVersion: 1,
          position: [680, 500],
          parameters: {
            workflowId: 'Message_Processor_Subflow',
            waitForExecution: true
          }
        },

        {
          id: 'execute-persistence',
          name: 'Execute Persistence',
          type: 'n8n-nodes-base.executeWorkflow',
          typeVersion: 1,
          position: [680, 580],
          parameters: {
            workflowId: 'Persistence_Memory_Subflow',
            waitForExecution: true
          }
        },

        // 9. Webhook Response - Resposta HTTP
        {
          id: 'webhook-response',
          name: 'Webhook Response',
          type: 'n8n-nodes-base.respondToWebhook',
          typeVersion: 1,
          position: [900, 300],
          parameters: {
            respondWith: 'json',
            responseBody: '{"status": "success", "message": "Message processed"}',
            options: {
              responseCode: 200
            }
          }
        }
      ],

      // Conexões entre os nós
      connections: {
        'WhatsApp Webhook': {
          main: [
            [
              {
                node: 'Extract Basic Data',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Extract Basic Data': {
          main: [
            [
              {
                node: 'Execute Normalize Extract',
                type: 'main',
                index: 0
              },
              {
                node: 'Execute User Registry',
                type: 'main',
                index: 0
              },
              {
                node: 'Execute Session Manager',
                type: 'main',
                index: 0
              },
              {
                node: 'Execute Onboarding',
                type: 'main',
                index: 0
              },
              {
                node: 'Execute Message Processor',
                type: 'main',
                index: 0
              },
              {
                node: 'Execute Persistence',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Execute Normalize Extract': {
          main: [
            [
              {
                node: 'Webhook Response',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Execute User Registry': {
          main: [
            [
              {
                node: 'Webhook Response',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Execute Session Manager': {
          main: [
            [
              {
                node: 'Webhook Response',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Execute Onboarding': {
          main: [
            [
              {
                node: 'Webhook Response',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Execute Message Processor': {
          main: [
            [
              {
                node: 'Webhook Response',
                type: 'main',
                index: 0
              }
            ]
          ]
        },
        'Execute Persistence': {
          main: [
            [
              {
                node: 'Webhook Response',
                type: 'main',
                index: 0
              }
            ]
          ]
        }
      },

      settings: {
        executionOrder: 'v1'
      }
    };
  }

  // Implementar o workflow
  async implement() {
    try {
      console.log('🚀 Iniciando implementação do Agent 1 - Unified User Pipeline...');
      
      // Verificar se workflow já existe
      const existingWorkflow = await this.client.findWorkflowByName(this.workflowName);
      
      // Criar estrutura do workflow
      const workflowData = this.createWorkflowStructure();
      
      let result;
      if (existingWorkflow) {
        console.log(`📝 Atualizando workflow existente: ${this.workflowName}`);
        result = await this.client.updateWorkflow(existingWorkflow.id, workflowData);
      } else {
        console.log(`🆕 Criando novo workflow: ${this.workflowName}`);
        result = await this.client.createWorkflow(workflowData);
      }
      
      // Ativar o workflow
      if (result.id) {
        console.log(`🔄 Ativando workflow: ${this.workflowName}`);
        await this.client.activateWorkflow(result.id);
      }
      
      console.log(`✅ Agent 1 implementado com sucesso!`);
      console.log(`📋 Workflow ID: ${result.id}`);
      console.log(`🌐 Webhook URL: ${CONFIG.n8nApiUrl}/webhook/whatsapp-webhook`);
      
      return result;
      
    } catch (error) {
      console.error('❌ Erro na implementação do Agent 1:', error);
      throw error;
    }
  }

  // Testar o workflow
  async test() {
    try {
      console.log('🧪 Testando Agent 1...');
      
      // Dados de teste simulando webhook WhatsApp
      const testData = {
        from: '5511999999999',
        body: 'Olá, teste do sistema',
        pushName: 'Usuário Teste',
        timestamp: Date.now(),
        messageType: 'text'
      };
      
      // Aqui poderia fazer uma requisição HTTP para o webhook
      console.log('📤 Dados de teste:', testData);
      console.log('✅ Teste preparado - use o webhook URL para testar');
      
    } catch (error) {
      console.error('❌ Erro no teste do Agent 1:', error);
      throw error;
    }
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  const agent = new Agent1UnifiedUserPipeline();
  
  agent.implement()
    .then(() => agent.test())
    .then(() => {
      console.log('🎉 Agent 1 - Unified User Pipeline implementado e testado com sucesso!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Falha na implementação:', error);
      process.exit(1);
    });
}

module.exports = Agent1UnifiedUserPipeline;

#!/usr/bin/env node

/**
 * 🚀 CRIADOR DO WORKFLOW UNIFICADO - WhatsApp AI Assistant
 * 
 * Este script cria um workflow simplificado que substitui os 7 workflows complexos
 * por uma solução elegante de 6 nós que mantém todo o contexto conversacional.
 * 
 * Funcionalidades:
 * - Recebe webhook WhatsApp
 * - Processa usuário e sessão em query unificada
 * - Gera resposta com IA contextualizada
 * - Envia resposta via Evolution API
 * - Mantém histórico completo de conversação
 */

const axios = require('axios');

// Configurações da API n8n
const N8N_API_URL = 'https://n8n-n8n.w9jo16.easypanel.host';
const N8N_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhcGkiLCJpYXQiOjE3NTYyMjEyMTEsImV4cCI6MTc1ODc3MjgwMH0.6BzCKbfcBOMtPukrVHFN1Mq2B4Fe8QezboF7CRp323M';

// Configuração do cliente HTTP
const apiClient = axios.create({
  baseURL: N8N_API_URL,
  headers: {
    'X-N8N-API-KEY': N8N_API_KEY,
    'Content-Type': 'application/json'
  }
});

/**
 * Definição do workflow unificado
 */
function createUnifiedWorkflowDefinition() {
  return {
    name: "WhatsApp_AI_Assistant_Unified",
    active: true,
    nodes: [
      // 1. Webhook WhatsApp - Ponto de entrada
      {
        parameters: {
          httpMethod: "POST",
          path: "whatsapp-unified",
          options: {}
        },
        id: "whatsapp-webhook",
        name: "WhatsApp Webhook",
        type: "n8n-nodes-base.webhook",
        typeVersion: 2,
        position: [100, 300],
        webhookId: "whatsapp-unified-webhook"
      },

      // 2. Extrair e Normalizar Dados
      {
        parameters: {
          jsCode: `// Extrair e normalizar dados do WhatsApp
const webhookData = $input.all()[0].json;

// Extrair dados principais
const from = webhookData.data?.key?.remoteJid || webhookData.from;
const message = webhookData.data?.message?.conversation || 
                webhookData.data?.message?.extendedTextMessage?.text || 
                webhookData.body || '';
const pushName = webhookData.data?.pushName || webhookData.pushName || 'Usuário';
const timestamp = new Date().toISOString();

// Normalizar telefone (remover caracteres especiais)
const phone = from.replace(/[^0-9]/g, '');

// Gerar CPF temporário baseado no phone (últimos 11 dígitos)
const cpf = phone.length >= 11 ? phone.slice(-11) : phone.padStart(11, '0');

// Dados normalizados
const normalizedData = {
  cpf: cpf,
  phone: phone,
  message: message.trim(),
  pushName: pushName,
  timestamp: timestamp,
  source: 'whatsapp',
  raw_data: webhookData
};

console.log('📱 Dados normalizados:', normalizedData);

return [{ json: normalizedData }];`
        },
        id: "extract-normalize",
        name: "Extrair e Normalizar",
        type: "n8n-nodes-base.code",
        typeVersion: 2,
        position: [300, 300]
      },

      // 3. Processar Usuário e Sessão (Query Unificada)
      {
        parameters: {
          operation: "executeQuery",
          query: `-- 🎯 QUERY UNIFICADA: Usuário + Sessão + Mensagem
WITH user_upsert AS (
  -- Buscar usuário existente ou criar novo
  INSERT INTO users (cpf, phone, name, access_level, active, last_access, onboarding_progress)
  VALUES (
    $1, 
    $2, 
    $3, 
    1, 
    true, 
    NOW(),
    '{"level": 1, "badges": [], "current_step": "welcome", "completed_steps": [], "gamification_score": 0}'::jsonb
  )
  ON CONFLICT (phone) 
  DO UPDATE SET 
    last_access = NOW(),
    name = COALESCE(users.name, $3)
  RETURNING cpf, phone, name, access_level, onboarding_progress, active, legacy_id
),
session_management AS (
  -- Buscar sessão ativa ou criar nova
  WITH active_session AS (
    SELECT cs.id, cs.user_id, cs.last_activity, cs.session_data
    FROM chat_sessions cs
    JOIN users u ON cs.user_id = u.legacy_id
    WHERE u.phone = $2 
      AND cs.active = true
      AND cs.last_activity > NOW() - INTERVAL '30 minutes'
    ORDER BY cs.last_activity DESC
    LIMIT 1
  )
  SELECT 
    COALESCE(
      (SELECT id FROM active_session),
      (
        INSERT INTO chat_sessions (user_id, channel_type, channel_identifier, active, last_activity, session_data)
        SELECT 
          (SELECT legacy_id FROM user_upsert),
          'whatsapp',
          $2,
          true,
          NOW(),
          jsonb_build_object('access_level', (SELECT access_level FROM user_upsert), 'device', 'whatsapp')
        RETURNING id
      ).id
    ) as session_id,
    COALESCE(
      (SELECT last_activity FROM active_session),
      NOW()
    ) as last_activity
),
message_insert AS (
  -- Inserir mensagem no histórico
  INSERT INTO chat_messages (
    session_id,
    user_id,
    content,
    message_type,
    direction,
    metadata
  )
  SELECT 
    (SELECT session_id FROM session_management),
    (SELECT legacy_id FROM user_upsert),
    $4,
    'text',
    'inbound',
    jsonb_build_object(
      'pushName', $3,
      'timestamp', $5,
      'source', 'whatsapp'
    )
  RETURNING id as message_id, created_at as message_timestamp
),
context_data AS (
  -- Buscar contexto da conversa (últimas 5 mensagens)
  SELECT 
    json_agg(
      json_build_object(
        'content', cm.content,
        'direction', cm.direction,
        'timestamp', cm.created_at
      ) ORDER BY cm.created_at DESC
    ) as conversation_history
  FROM chat_messages cm
  JOIN session_management sm ON cm.session_id = sm.session_id
  WHERE cm.created_at >= NOW() - INTERVAL '1 hour'
  LIMIT 5
)
-- Resultado final consolidado
SELECT 
  u.cpf,
  u.phone,
  u.name,
  u.access_level,
  u.onboarding_progress,
  sm.session_id,
  mi.message_id,
  mi.message_timestamp,
  cd.conversation_history,
  $4 as current_message,
  'ready_for_ai' as status
FROM user_upsert u
CROSS JOIN session_management sm
CROSS JOIN message_insert mi
CROSS JOIN context_data cd;`,
          options: {
            queryReplacement: "={{ $json.cpf }},={{ $json.phone }},={{ $json.pushName }},={{ $json.message }},={{ $json.timestamp }}"
          }
        },
        id: "process-user-session",
        name: "Processar Usuário e Sessão",
        type: "n8n-nodes-base.postgres",
        typeVersion: 2,
        position: [500, 300],
        credentials: {
          postgres: {
            id: "tqtLhtoqDEUD7G1k",
            name: "assistente_v0.1"
          }
        }
      },

      // 4. Processar com IA (OpenAI)
      {
        parameters: {
          resource: "chat",
          operation: "create",
          model: "gpt-4",
          messages: {
            values: [
              {
                role: "system",
                content: `Você é o assistente de IA da "AI Comtxae", uma plataforma de automação comunitária.

CONTEXTO DO USUÁRIO:
- Nome: {{ $json.name }}
- Nível de Acesso: {{ $json.access_level }}
- Progresso: {{ $json.onboarding_progress }}

HISTÓRICO DA CONVERSA:
{{ $json.conversation_history }}

INSTRUÇÕES:
1. Seja amigável e prestativo
2. Use o nome do usuário quando apropriado
3. Mantenha respostas concisas (máximo 200 caracteres para WhatsApp)
4. Se for primeira interação, dê boas-vindas
5. Ofereça ajuda baseada no nível de acesso do usuário

Responda de forma natural e contextualizada.`
              },
              {
                role: "user",
                content: "={{ $json.current_message }}"
              }
            ]
          },
          options: {
            temperature: 0.7,
            maxTokens: 150
          }
        },
        id: "ai-processing",
        name: "Processar com IA",
        type: "n8n-nodes-base.openAi",
        typeVersion: 1,
        position: [700, 300],
        credentials: {
          openAiApi: {
            id: "openai-credentials",
            name: "OpenAI API"
          }
        }
      },

      // 5. Enviar Resposta via Evolution API
      {
        parameters: {
          method: "POST",
          url: "https://evolution-evolution-api.w9jo16.easypanel.host/message/sendText/AI_Comtxae",
          authentication: "genericCredentialType",
          genericAuthType: "httpHeaderAuth",
          httpHeaderAuth: {
            name: "apikey",
            value: "D04C98B9077B-456A-8BCA-4C5892630893"
          },
          sendBody: true,
          bodyContentType: "json",
          jsonBody: `{
  "number": "{{ $('Processar Usuário e Sessão').item.json.phone }}",
  "text": "{{ $('Processar com IA').item.json.choices[0].message.content }}",
  "delay": 1000
}`,
          options: {}
        },
        id: "send-response",
        name: "Enviar Resposta",
        type: "n8n-nodes-base.httpRequest",
        typeVersion: 4,
        position: [900, 300]
      },

      // 6. Webhook Response
      {
        parameters: {
          respondWith: "json",
          responseBody: `{
  "status": "success",
  "message": "Mensagem processada com sucesso",
  "user_cpf": "{{ $('Processar Usuário e Sessão').item.json.cpf }}",
  "session_id": "{{ $('Processar Usuário e Sessão').item.json.session_id }}",
  "ai_response": "{{ $('Processar com IA').item.json.choices[0].message.content }}",
  "timestamp": "{{ new Date().toISOString() }}"
}`
        },
        id: "webhook-response",
        name: "Webhook Response",
        type: "n8n-nodes-base.webhookResponse",
        typeVersion: 1,
        position: [1100, 300]
      }
    ],

    // Conexões entre os nós
    connections: {
      "WhatsApp Webhook": {
        main: [
          [
            {
              node: "Extrair e Normalizar",
              type: "main",
              index: 0
            }
          ]
        ]
      },
      "Extrair e Normalizar": {
        main: [
          [
            {
              node: "Processar Usuário e Sessão",
              type: "main",
              index: 0
            }
          ]
        ]
      },
      "Processar Usuário e Sessão": {
        main: [
          [
            {
              node: "Processar com IA",
              type: "main",
              index: 0
            }
          ]
        ]
      },
      "Processar com IA": {
        main: [
          [
            {
              node: "Enviar Resposta",
              type: "main",
              index: 0
            }
          ]
        ]
      },
      "Enviar Resposta": {
        main: [
          [
            {
              node: "Webhook Response",
              type: "main",
              index: 0
            }
          ]
        ]
      }
    },

    settings: {
      executionOrder: "v1"
    },

    meta: {
      templateCredsSetupCompleted: true
    }
  };
}

/**
 * Criar o workflow via API n8n
 */
async function createUnifiedWorkflow() {
  try {
    console.log('🚀 Criando workflow unificado WhatsApp AI Assistant...');
    
    const workflowDefinition = createUnifiedWorkflowDefinition();
    
    const response = await apiClient.post('/api/v1/workflows', workflowDefinition);
    
    if (response.status === 201 || response.status === 200) {
      console.log('✅ Workflow criado com sucesso!');
      console.log(`📋 ID: ${response.data.id}`);
      console.log(`🌐 Webhook URL: ${N8N_API_URL}/webhook/whatsapp-unified`);
      console.log('\n🎯 CARACTERÍSTICAS DO WORKFLOW:');
      console.log('   • 6 nós simples e elegantes');
      console.log('   • Query SQL unificada (usuário + sessão + mensagem)');
      console.log('   • IA contextualizada com histórico');
      console.log('   • Resposta automática via Evolution API');
      console.log('   • Performance otimizada');
      
      return response.data;
    } else {
      throw new Error(`Erro HTTP: ${response.status}`);
    }
    
  } catch (error) {
    console.error('❌ Erro ao criar workflow:', error.response?.data || error.message);
    throw error;
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  createUnifiedWorkflow()
    .then(() => {
      console.log('\n🎉 Workflow unificado criado com sucesso!');
      console.log('💡 Este workflow substitui os 7 workflows complexos por uma solução elegante.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Falha na criação:', error);
      process.exit(1);
    });
}

module.exports = { createUnifiedWorkflow };
